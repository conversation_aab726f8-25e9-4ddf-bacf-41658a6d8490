-- Migration to add super admin support for platform operators
-- Super admins are platform operators, NOT tenant users
-- They exist in auth.users but not in tenants.users

-- Add is_super_admin column to auth.users.raw_user_meta_data
-- This will be set manually for platform operators like j<PERSON><PERSON><PERSON><PERSON>@gmail.com

-- Create a function to check if a user is a super admin
CREATE OR REPLACE FUNCTION auth.is_super_admin(user_id uuid DEFAULT auth.uid())
RETURNS boolean
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
  SELECT COALESCE(
    (raw_user_meta_data->>'is_super_admin')::boolean,
    false
  )
  FROM auth.users
  WHERE id = user_id;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION auth.is_super_admin(uuid) TO authenticated, service_role;
