/**
 * E2E tests for Super Admin Guard functionality
 */

describe('Super Admin Guard', () => {
  beforeEach(() => {
    // Clear any existing sessions
    cy.clearCookies();
    cy.clearLocalStorage();
  });

  describe('Unauthorized Access', () => {
    it('should redirect unauthenticated users to login', () => {
      // Try to access superadmin route without authentication
      cy.visit('/superadmin', { failOnStatusCode: false });
      
      // Should be redirected to login page
      cy.url().should('include', '/loginadmin');
      cy.url().should('include', 'redirectedFrom=%2Fsuperadmin');
    });

    it('should redirect non-super-admin users to dashboard', () => {
      // Mock a regular user session (partner role but not super admin)
      cy.window().then((win) => {
        // Mock localStorage with a regular user session
        const mockSession = {
          access_token: createMockJWT({
            sub: 'regular-user-id',
            email: '<EMAIL>',
            role: 'partner',
            tenant_id: 'test-tenant',
            is_super_admin: false,
            exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
          }),
          refresh_token: 'mock-refresh-token',
          user: {
            id: 'regular-user-id',
            email: '<EMAIL>'
          }
        };
        
        win.localStorage.setItem('sb-anwefmklplkjxkmzpnva-auth-token', JSON.stringify(mockSession));
      });

      // Try to access superadmin route
      cy.visit('/superadmin', { failOnStatusCode: false });
      
      // Should be redirected to dashboard with error
      cy.url().should('include', '/dashboard');
      cy.url().should('include', 'error=superadmin_unauthorized');
    });
  });

  describe('Authorized Access', () => {
    it('should allow super admin users to access superadmin routes', () => {
      // Mock a super admin user session
      cy.window().then((win) => {
        const mockSession = {
          access_token: createMockJWT({
            sub: 'super-admin-user-id',
            email: '<EMAIL>',
            role: 'partner',
            tenant_id: 'test-tenant',
            is_super_admin: true,
            exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
          }),
          refresh_token: 'mock-refresh-token',
          user: {
            id: 'super-admin-user-id',
            email: '<EMAIL>'
          }
        };
        
        win.localStorage.setItem('sb-anwefmklplkjxkmzpnva-auth-token', JSON.stringify(mockSession));
      });

      // Visit superadmin route
      cy.visit('/superadmin');
      
      // Should successfully load the superadmin page
      cy.url().should('include', '/superadmin');
      cy.contains('Admin Portal').should('be.visible');
    });

    it('should allow navigation between superadmin pages', () => {
      // Mock a super admin user session
      cy.window().then((win) => {
        const mockSession = {
          access_token: createMockJWT({
            sub: 'super-admin-user-id',
            email: '<EMAIL>',
            role: 'partner',
            tenant_id: 'test-tenant',
            is_super_admin: true,
            exp: Math.floor(Date.now() / 1000) + 3600
          }),
          refresh_token: 'mock-refresh-token',
          user: {
            id: 'super-admin-user-id',
            email: '<EMAIL>'
          }
        };
        
        win.localStorage.setItem('sb-anwefmklplkjxkmzpnva-auth-token', JSON.stringify(mockSession));
      });

      // Visit superadmin route
      cy.visit('/superadmin');
      
      // Navigate to different superadmin pages
      cy.contains('Models').click();
      cy.url().should('include', '/superadmin/models');
      
      cy.contains('Prompts').click();
      cy.url().should('include', '/superadmin/prompts');
      
      cy.contains('Security').click();
      cy.url().should('include', '/superadmin/security');
    });
  });
});

/**
 * Helper function to create a mock JWT token for testing
 * Note: This is a simplified version for testing purposes only
 */
function createMockJWT(payload: any): string {
  // Create a simple base64 encoded token for testing
  // In real implementation, this would be properly signed
  const header = { alg: 'HS256', typ: 'JWT' };
  const encodedHeader = btoa(JSON.stringify(header));
  const encodedPayload = btoa(JSON.stringify(payload));
  const signature = 'mock-signature';
  
  return `${encodedHeader}.${encodedPayload}.${signature}`;
}
