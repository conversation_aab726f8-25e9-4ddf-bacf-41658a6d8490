# Super-Admin Pages Audit

**Date**: 2025-01-04  
**Purpose**: Catalogue existing Super-Admin interface for Voice Receptionist integration  
**Branch**: `chore/super-admin-audit`

## 1. Route Map

### Super Admin Routes (`/superadmin/*`)

| Route                                | Component                                      | Purpose                    | RBAC/Feature Flag |
| ------------------------------------ | ---------------------------------------------- | -------------------------- | ----------------- |
| `/superadmin`                        | `frontend/src/app/superadmin/page.tsx`         | Main dashboard             | None detected     |
| `/superadmin/calendar`               | `frontend/src/app/superadmin/calendar/page.tsx` | Calendar health monitoring | None detected     |
| `/superadmin/models`                 | `frontend/src/app/superadmin/models/page.tsx`  | LLM model management       | None detected     |
| `/superadmin/prompts`                | `frontend/src/app/superadmin/prompts/page.tsx` | Prompt template management | None detected     |
| `/superadmin/resource-usage`         | `frontend/src/app/superadmin/resource-usage/page.tsx` | Resource analytics | None detected     |
| `/superadmin/security`               | `frontend/src/app/superadmin/security/page.tsx` | Security overview        | None detected     |
| `/superadmin/subscription-management` | `frontend/src/app/superadmin/subscription-management/page.tsx` | Subscription control | None detected |
| `/superadmin/tenant-quotas`          | `frontend/src/app/superadmin/tenant-quotas/page.tsx` | Tenant quota management | None detected |
| `/superadmin/tenant-subscriptions`   | `frontend/src/app/superadmin/tenant-subscriptions/page.tsx` | Tenant subscription view | None detected |

### Dashboard Admin Routes (`/admin/*`)

| Route                     | Component                                           | Purpose                | RBAC/Feature Flag      |
| ------------------------- | --------------------------------------------------- | ---------------------- | ---------------------- |
| `/admin`                  | `frontend/src/app/(dashboard)/admin/page.tsx`      | Admin dashboard        | `UserRole.Partner`     |
| `/admin/subscriptions`    | `frontend/src/app/(dashboard)/admin/subscriptions/page.tsx` | Subscription mgmt | `UserRole.Partner`     |
| `/admin/usage`            | `frontend/src/app/(dashboard)/admin/usage/page.tsx` | Usage monitoring      | `UserRole.Partner`     |
| `/admin/security`         | `frontend/src/app/(dashboard)/admin/security/page.tsx` | Security dashboard | `UserRole.Partner`     |
| `/admin/security/alerts`  | `frontend/src/app/(dashboard)/admin/security/alerts/*` | Security alerts   | `UserRole.Partner`     |
| `/admin/security/anomalies` | `frontend/src/app/(dashboard)/admin/security/anomalies/*` | Anomaly detection | `UserRole.Partner` |
| `/admin/security/tokens`  | `frontend/src/app/(dashboard)/admin/security/tokens/*` | Token management  | `UserRole.Partner`     |
| `/admin/security/settings` | `frontend/src/app/(dashboard)/admin/security/settings/*` | Security config | `UserRole.Partner`   |
| `/admin/voice-agents`     | **Missing Implementation**                          | Voice agent mgmt       | `UserRole.Partner`     |

### Security Dashboard Routes (`/(admin)/*`)

| Route                    | Component                                      | Purpose           | RBAC/Feature Flag |
| ------------------------ | ---------------------------------------------- | ----------------- | ----------------- |
| `/security-dashboard`    | `frontend/src/app/(admin)/security-dashboard/page.tsx` | Security overview | None detected     |

## 2. Role & Permission Handling

### Role Decoding & Context

| Hook/Context      | Path                                    | Description                           |
| ----------------- | --------------------------------------- | ------------------------------------- |
| `useUser()`       | `frontend/src/contexts/UserContext.tsx` | JWT-based user context with role     |
| `useRbac()`       | `frontend/src/hooks/useRbac.ts`         | Role-based access control utilities  |
| `RoleBasedComponent` | `frontend/src/components/auth/RoleBasedComponent.tsx` | Conditional rendering by role |

### Role Enforcement Patterns

**JWT Claims Source**: `UserContext` extracts role from `session.access_token` via `parseJwtPayload()`

**Role Hierarchy**:
- `partner` - Admin access (`rbac.isAdmin()` returns true)
- `attorney`, `paralegal`, `staff` - Staff access (`rbac.isStaff()` returns true)  
- `client` - Client access (`rbac.isClient()` returns true)

**Admin Layout Guard**: `frontend/src/app/(dashboard)/admin/layout.tsx`
```typescript
const VALID_ADMIN_ROLES: ReadonlySet<UserRole> = new Set([UserRole.Partner]);
```

**Super Admin Access**: No explicit guards detected - relies on manual URL access

## 3. Feature-Flag Usage

### Subscription-Based Features

| Service/Function | Path                                              | Description                    |
| ---------------- | ------------------------------------------------- | ------------------------------ |
| `checkFeatureAccess()` | `frontend/src/lib/services/subscription-service.ts:755` | Tenant feature validation |
| `subscriptionMiddleware` | `frontend/src/lib/middleware/subscription-middleware.ts:30` | API feature gating |
| `check_tenant_feature_access()` | Database RPC function                      | Server-side feature validation |

### Environment-Based Flags

| Flag Function    | Path                                    | Description              |
| ---------------- | --------------------------------------- | ------------------------ |
| `isAGUIEnabled()` | `frontend/src/lib/features/ag-ui.ts:9` | AG-UI feature toggle     |
| `getFeatureFlags()` | `frontend/src/lib/features/ag-ui.ts:76` | Comprehensive flag object |

### Feature Access Patterns

**RBAC Feature Check**: `useRbac().hasAccess(feature)`
- `'templates'` - Partner, attorney, staff
- `'documents'` - Partner, attorney, paralegal, staff, client  
- `'cases'` - Partner, attorney, paralegal, staff
- `'admin'` - Partner only

**Subscription Feature Check**: `subscriptionService.checkFeatureAccess(tenantId, featureKey)`

## 4. UI Layout & Component Library

### Navigation Structure

| Component        | Path                                           | Purpose                    |
| ---------------- | ---------------------------------------------- | -------------------------- |
| `AdminSidebar`   | `frontend/src/components/admin/admin-sidebar.tsx` | Dashboard admin navigation |
| `Sidebar`        | `frontend/src/components/layout/sidebar.tsx`   | Main app navigation        |
| Superadmin Layout | `frontend/src/app/superadmin/layout.tsx`      | Superadmin navigation      |

### Reusable Components

| Component Category | Examples                                        | Usage Pattern              |
| ------------------ | ----------------------------------------------- | -------------------------- |
| **Tables**         | `Table`, `DataTable`, `TableHeader`, `TableRow` | Standard data display      |
| **Cards**          | `Card`, `CardHeader`, `CardContent`            | Content containers         |
| **Forms**          | `Input`, `Button`, `Select`, `Textarea`        | User input                 |
| **Dialogs**        | `Dialog`, `Drawer`, `AlertDialog`              | Modal interactions         |
| **Data Display**   | `Badge`, `Progress`, `Tabs`                    | Status & organization      |

### Admin-Specific Components

| Component                    | Path                                                    | Purpose                |
| ---------------------------- | ------------------------------------------------------- | ---------------------- |
| `VoiceAgentUsageDashboard`   | `frontend/src/components/admin/usage/voice-agent-usage-dashboard.tsx` | Voice usage metrics |
| `TokenUsageDashboard`        | `frontend/src/components/admin/usage/token-usage-dashboard.tsx` | Token usage metrics |
| `SubscriptionPlansTable`     | `frontend/src/components/admin/subscription/subscription-plans-table.tsx` | Plan management |
| `TenantSubscriptionsTable`   | `frontend/src/components/admin/subscription/tenant-subscriptions-table.tsx` | Tenant subs |

## 5. Build & Deploy Pipeline

### GitHub Actions Workflows

| Workflow                    | Path                                        | Purpose                    |
| --------------------------- | ------------------------------------------- | -------------------------- |
| `ci.yml`                    | `.github/workflows/ci.yml`                 | Main CI pipeline           |
| `typescript-check.yml`      | `.github/workflows/typescript-check.yml`   | TypeScript validation      |
| `type-check.yml`            | `.github/workflows/type-check.yml`         | Additional type checking   |
| `auth-tests.yml`            | `.github/workflows/auth-tests.yml`         | Authentication testing     |
| `subscription-tests.yml`    | `.github/workflows/subscription-tests.yml` | Subscription testing       |

### Environment Variables (CI)

```yaml
# Supabase
SUPABASE_URL: http://dummy.local
SUPABASE_KEY: dummy
NEXT_PUBLIC_SUPABASE_URL: http://dummy.local
NEXT_PUBLIC_SUPABASE_ANON_KEY: dummy

# API Keys  
OPENAI_API_KEY: dummy
PINECONE_API_KEY: dummy

# Services
PROMPT_MGR_URL: http://localhost:8000/prompt-mgr/api/v1
```

### Build Process

1. **Frontend**: Next.js build with TypeScript checking
2. **Backend**: Python dependency installation and import validation
3. **Testing**: Pytest for backend, Jest/Cypress for frontend
4. **Deployment**: No Vercel configuration detected in audit scope

## 6. Gaps & Recommendations

### Voice Receptionist Integration Points

#### ✅ **Ready for Integration**

1. **Voice Usage Dashboard** - Already exists at `VoiceAgentUsageDashboard`
2. **Admin Sidebar** - Already includes "Voice Agents" menu item (`/admin/voice-agents`)
3. **Component Library** - Rich set of reusable UI components available

#### ⚠️ **Missing Implementations**

1. **Voice Agent Management Page** - `/admin/voice-agents` route exists in sidebar but no page
2. **Call Logs Interface** - No call history/logs component found
3. **Phone Number Management** - No phone number assignment interface
4. **Failed Notifications Panel** - No notification failure tracking UI

#### 🔧 **Recommended Implementation Strategy**

**Phase 1: Core Voice Panels**
```
/admin/voice-agents/
├── page.tsx              # Main voice agent dashboard
├── calls/
│   └── page.tsx          # Call logs and history  
├── numbers/
│   └── page.tsx          # Phone number management
└── notifications/
    └── page.tsx          # Failed notification tracking
```

**Phase 2: Navigation Integration**
- Extend `AdminSidebar` with voice sub-menu
- Add voice-specific icons and navigation states
- Implement breadcrumb navigation for voice sections

**Phase 3: RBAC & Feature Flags**
- Add voice-specific feature flags: `hasVoiceAgent`, `hasOutboundCalling`
- Implement subscription-based voice feature gating
- Add voice admin permissions to role hierarchy

### Required RBAC Scopes

```typescript
// Add to useRbac.hasAccess()
case 'voice_admin':
  return role === 'partner'
case 'voice_calls':  
  return ['partner', 'attorney', 'staff'].includes(role)
case 'voice_numbers':
  return role === 'partner'
```

### Required Feature Flags

```typescript
// Add to subscription features
'hasVoiceAgent': boolean      // Basic voice receptionist
'hasOutboundCalling': boolean // Outbound call capability  
'hasCallRecording': boolean   // Call recording feature
'hasVoiceAnalytics': boolean  // Advanced voice analytics
```

---

**Next Steps**: Implement missing voice agent pages using existing component patterns and integrate with current admin navigation structure.
