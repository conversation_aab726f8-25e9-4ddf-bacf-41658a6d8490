"""
Calendly scheduling API routes.

This module provides API routes for creating Calendly scheduling links.
"""

import os
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field

from backend.db.supabase_client import get_supabase_client
from backend.middleware.auth_middleware import UserContext, get_current_user
from backend.services.calendly import (
    SchedulingLinkRequest,
    create_scheduling_link,
)
from backend.utils.logging import get_logger

# Configure logging
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api/calendly", tags=["calendly"])


class CreateSchedulingLinkRequest(BaseModel):
    """Request model for creating a scheduling link."""

    event_type_uri: Optional[str] = Field(
        None,
        description=(
            "The Calendly event type URI "
            "(defaults to CALENDLY_EVENT_TYPE_PI env var)"
        ),
    )
    prospect_name: Optional[str] = Field(
        None, description="The name of the prospect"
    )
    prospect_email: Optional[str] = Field(
        None, description="The email of the prospect"
    )
    prospect_phone: Optional[str] = Field(
        None, description="The phone number of the prospect"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata for the booking"
    )


class CreateSchedulingLinkResponse(BaseModel):
    """Response model for a created scheduling link."""

    url: str = Field(..., description="The scheduling link URL")
    booking_id: uuid.UUID = Field(..., description="The booking ID in our system")


@router.post("/scheduling-links", response_model=CreateSchedulingLinkResponse)
async def create_scheduling_link_endpoint(
    request_data: CreateSchedulingLinkRequest,
    user: UserContext = Depends(get_current_user),  # noqa: B008
):
    """
    Create a single-use scheduling link.

    Args:
        request_data: The scheduling link request
        request: The FastAPI request object
        user: The current user

    Returns:
        CreateSchedulingLinkResponse: The created scheduling link
    """
    # Use the default event type URI if not provided
    event_type_uri = request_data.event_type_uri or os.getenv("CALENDLY_EVENT_TYPE_PI")

    if not event_type_uri:
        raise HTTPException(
            status_code=400,
            detail="Event type URI not provided and CALENDLY_EVENT_TYPE_PI not set"
        )

    try:
        # Create the scheduling link
        calendly_request = SchedulingLinkRequest(
            event_type_uri=event_type_uri,
            owner_name=user.email,  # Use email as name since name field doesn't exist
            owner_email=user.email,
            prospect_name=request_data.prospect_name,
            prospect_email=request_data.prospect_email,
            prospect_phone=request_data.prospect_phone,
            firm_id=user.firm_id,
            metadata=request_data.metadata
        )

        calendly_response = await create_scheduling_link(calendly_request)

        # Create a booking record in the database
        supabase = await get_supabase_client()

        booking_data = {
            "firm_id": str(user.firm_id),
            "user_id": str(user.user_id),
            "provider": "calendly",
            "event_type_uri": event_type_uri,
            "scheduling_link": calendly_response.url,
            "status": "pending",
            "source": "core",
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }

        # Add prospect info if available
        if request_data.prospect_name:
            booking_data["attendee_name"] = request_data.prospect_name

        if request_data.prospect_email:
            booking_data["attendee_email"] = request_data.prospect_email

        if request_data.prospect_phone:
            booking_data["attendee_phone"] = request_data.prospect_phone

        # Add metadata if available
        if request_data.metadata:
            booking_data["metadata"] = request_data.metadata

        # Insert the booking
        result = await supabase.table("tenants.bookings") \
            .insert(booking_data) \
            .execute()

        if not result.data:
            raise HTTPException(
                status_code=500,
                detail="Failed to create booking record"
            )

        booking_id = result.data[0].get("id")

        logger.info(
            "Created scheduling link",
            extra={
                "booking_id": booking_id,
                "firm_id": str(user.firm_id),
                "user_id": str(user.user_id),
                "event_type_uri": event_type_uri
            }
        )

        return CreateSchedulingLinkResponse(
            url=calendly_response.url,
            booking_id=booking_id
        )

    except Exception as e:
        logger.error(
            "Error creating scheduling link",
            extra={
                "firm_id": str(user.firm_id),
                "user_id": str(user.user_id),
                "error": str(e)
            },
            exc_info=True
        )

        raise HTTPException(
            status_code=500,
            detail=f"Failed to create scheduling link: {str(e)}",
        ) from e
