// frontend/src/middleware.ts
import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";
import type { Database } from "@/lib/supabase/database.types";
import { UserRole } from "@/lib/types/auth";
import { parseJwtPayload } from "@/lib/supabase/client";

// Define protected routes and their required roles (using the standardized UserRole type)
const protectedRoutes: Record<string, UserRole[]> = {
  "/admin": [UserRole.Partner], // Regular admin dashboard (only partners)
  "/dashboard": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ], // Staff dashboard
  "/client-portal": [UserRole.Client], // Client portal
  "/cases": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/documents": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/settings": [UserRole.Admin], // Tenant settings (Tenant Admins only)
  "/billing": [UserRole.Partner], // SaaS Billing (Partners only)
  "/users": [UserRole.Admin], // Tenant user management (Tenant Admins only)
  "/calendar": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/tasks": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/clients": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/submit-case": [UserRole.Client],
  // Note: /superadmin is handled separately by JWT is_super_admin claim
};

export async function middleware(request: NextRequest): Promise<NextResponse> {
  let response = NextResponse.next({ request });

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({ name, value, ...options });
          response = NextResponse.next({ request });
          response.cookies.set({ name, value, ...options });
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({ name, value: "", ...options });
          response = NextResponse.next({ request });
          response.cookies.delete({ name, ...options });
        },
      },
    },
  );

  const {
    data: { user },
  } = await supabase.auth.getUser();
  console.log(
    "Middleware: User object from supabase.auth.getUser():",
    JSON.stringify(user, null, 2),
  );
  console.log(
    "Middleware: Extracted user_metadata:",
    JSON.stringify(user?.user_metadata, null, 2),
  );
  const currentPath = request.nextUrl.pathname;

  // --- Authentication Check ---
  const isProtectedRoute = Object.keys(protectedRoutes).some((route) =>
    currentPath.startsWith(route),
  );
  const isSuperAdminRoute = currentPath.startsWith("/superadmin");

  if (!user && (isProtectedRoute || isSuperAdminRoute)) {
    const loginUrl = isSuperAdminRoute ? "/loginadmin" : "/login";
    const redirectUrl = new URL(loginUrl, request.url);
    redirectUrl.searchParams.set("redirectedFrom", currentPath);
    console.log(
      `Middleware: Unauthenticated access to ${currentPath}. Redirecting to ${loginUrl}.`,
    );
    return NextResponse.redirect(redirectUrl);
  }

  // --- Authorization Check (only if user is authenticated) ---
  if (user) {
    const userEmail = user.email;

    // 1. Superadmin Route Check (by JWT is_super_admin claim)
    if (isSuperAdminRoute) {
      // Get the session to access the JWT token
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        console.warn(`Middleware: No access token found for superadmin route check. Redirecting.`);
        return NextResponse.redirect(
          new URL("/loginadmin?error=superadmin_unauthorized", request.url),
        );
      }

      // Parse JWT to check is_super_admin claim
      const jwtPayload = parseJwtPayload(session.access_token);
      const isSuperAdmin = jwtPayload?.is_super_admin === true;

      if (!isSuperAdmin) {
        console.warn(
          `Middleware: Unauthorized attempt on /superadmin by ${userEmail}. User is not a super admin. Redirecting.`,
        );
        return NextResponse.redirect(
          new URL("/dashboard?error=superadmin_unauthorized", request.url),
        );
      }

      console.log(`Middleware: Super admin access granted to ${userEmail}`);
      // Allow superadmin access
      return response;
    }

    // 2. Other Protected Routes Check (by role)
    // Check if user metadata and roles exist
    if (!user?.id || !user.app_metadata?.roles) {
      console.log("Middleware: User ID or roles missing, redirecting to login");
      const loginUrl = "/login";
      const redirectUrl = new URL(loginUrl, request.url);
      redirectUrl.searchParams.set("redirectedFrom", currentPath);
      return NextResponse.redirect(redirectUrl);
    }

    const userRoles: UserRole[] = user.app_metadata.roles as UserRole[];

    console.log(
      `Middleware: Authenticated user ${user.email} with roles:`,
      userRoles,
    );

    for (const routePrefix in protectedRoutes) {
      if (currentPath.startsWith(routePrefix)) {
        const requiredRoles = protectedRoutes[routePrefix];

        // ADDED DEBUG LOG: What roles did we actually validate?
        console.log(
          `Middleware: Checking path ${currentPath}. User roles validated: [${userRoles.join(
            ", ",
          )}]. Required roles: [${requiredRoles.join(", ")}]`,
        );

        // Check if the user has at least one of the required roles
        const hasRequiredRole = userRoles.some((userRole) =>
          requiredRoles.includes(userRole),
        );

        if (!hasRequiredRole) {
          console.warn(
            `Middleware: Role mismatch for ${currentPath}. User ${userEmail} (roles: ${userRoles.join(",") || "none"}) lacks required roles: ${requiredRoles.join(", ")}. Redirecting.`,
          );
          // Redirect unauthorized roles away
          const defaultRedirect = userRoles.includes(UserRole.Client)
            ? "/client-portal"
            : "/dashboard";
          return NextResponse.redirect(
            new URL(defaultRedirect + "?error=role_unauthorized", request.url),
          );
        }
        // Role is valid for this route, break the loop
        console.log(
          `Middleware: Roles '${userRoles.join(",")}' authorized for ${currentPath}.`,
        );
        break;
      }
    }
  }

  // Allow request to proceed for public routes or authorized users
  return response;
}

// Config to specify which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api (API routes)
     * - auth (auth routes like login, callback, etc.)
     * - Explicitly public routes
     */
    "/((?!_next/static|_next/image|favicon.ico|api|auth|login|loginadmin|register|privacy-policy|terms).*)",
  ],
};
