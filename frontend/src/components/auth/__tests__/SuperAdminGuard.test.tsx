/**
 * Tests for SuperAdminGuard component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { SuperAdminGuard } from '../SuperAdminGuard';
import { useRbac } from '@/hooks/useRbac';
import { useUser } from '@/contexts/UserContext';

// Mock the hooks and router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/hooks/useRbac', () => ({
  useRbac: jest.fn(),
}));

jest.mock('@/contexts/UserContext', () => ({
  useUser: jest.fn(),
}));

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUseRbac = useRbac as jest.MockedFunction<typeof useRbac>;
const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;

const mockReplace = jest.fn();

describe('SuperAdminGuard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue({
      replace: mockReplace,
    } as any);
  });

  const TestComponent = () => <div data-testid="protected-content">Protected Content</div>;

  describe('when user is loading', () => {
    beforeEach(() => {
      mockUseRbac.mockReturnValue({
        isSuperAdmin: jest.fn(() => false),
        isLoading: jest.fn(() => true),
      } as any);
      
      mockUseUser.mockReturnValue({
        loading: true,
      } as any);
    });

    it('should show loading spinner by default', () => {
      render(
        <SuperAdminGuard>
          <TestComponent />
        </SuperAdminGuard>
      );

      expect(screen.getByRole('status', { hidden: true })).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should not show loading spinner when showLoading is false', () => {
      render(
        <SuperAdminGuard showLoading={false}>
          <TestComponent />
        </SuperAdminGuard>
      );

      expect(screen.queryByRole('status', { hidden: true })).not.toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('when user is not a super admin', () => {
    beforeEach(() => {
      mockUseRbac.mockReturnValue({
        isSuperAdmin: jest.fn(() => false),
        isLoading: jest.fn(() => false),
      } as any);
      
      mockUseUser.mockReturnValue({
        loading: false,
      } as any);
    });

    it('should redirect to default fallback path', () => {
      render(
        <SuperAdminGuard>
          <TestComponent />
        </SuperAdminGuard>
      );

      expect(mockReplace).toHaveBeenCalledWith('/');
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should redirect to custom fallback path', () => {
      render(
        <SuperAdminGuard fallbackPath="/dashboard">
          <TestComponent />
        </SuperAdminGuard>
      );

      expect(mockReplace).toHaveBeenCalledWith('/dashboard');
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });

    it('should not render children', () => {
      render(
        <SuperAdminGuard>
          <TestComponent />
        </SuperAdminGuard>
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });

  describe('when user is a super admin', () => {
    beforeEach(() => {
      mockUseRbac.mockReturnValue({
        isSuperAdmin: jest.fn(() => true),
        isLoading: jest.fn(() => false),
      } as any);
      
      mockUseUser.mockReturnValue({
        loading: false,
      } as any);
    });

    it('should render children', () => {
      render(
        <SuperAdminGuard>
          <TestComponent />
        </SuperAdminGuard>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      expect(mockReplace).not.toHaveBeenCalled();
    });

    it('should not show loading spinner', () => {
      render(
        <SuperAdminGuard>
          <TestComponent />
        </SuperAdminGuard>
      );

      expect(screen.queryByRole('status', { hidden: true })).not.toBeInTheDocument();
    });
  });
});
