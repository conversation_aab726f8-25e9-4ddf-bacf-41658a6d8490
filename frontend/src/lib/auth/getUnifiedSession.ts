/**
 * Unified Session Helper
 * 
 * This helper provides a unified interface for session management that can switch
 * between Supabase Auth and legacy cookie/JWT authentication based on a feature flag.
 * 
 * When USE_SUPABASE_AUTH=true: Uses Supabase session management
 * When USE_SUPABASE_AUTH=false: Falls back to legacy cookie/JWT parsing
 */

import type { ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies';
import type { Session, User } from '@supabase/supabase-js';
import { createServerClientForUser } from '@/lib/supabaseClient';
import { parseJwtPayload, type JwtPayload } from '@/lib/supabase/client';

// Type for cookies that works with both middleware and server components
type CookiesLike = {
  get(name: string): { value: string } | undefined;
};

// Unified session interface that works with both Supabase and legacy auth
export interface UnifiedSession {
  user: {
    id: string;
    email?: string;
    app_metadata?: {
      roles?: string[];
      [key: string]: any;
    };
    user_metadata?: {
      [key: string]: any;
    };
  };
  access_token: string;
  refresh_token?: string;
  expires_at?: number;
  expires_in?: number;
  token_type?: string;
  // Additional fields for compatibility
  tenant_id?: string;
  role?: string;
  // Source indicates which auth system provided this session
  source: 'supabase' | 'legacy';
}

/**
 * Legacy session extraction from cookies
 * This preserves the existing cookie/JWT authentication logic
 */
async function legacyGetSessionFromCookie(cookies: CookiesLike): Promise<UnifiedSession | null> {
  try {
    // Look for various cookie patterns used in the existing system
    const cookieNames = [
      'sb-anwefmklplkjxkmzpnva-auth-token',
      'sb-access-token',
      'sb-auth-token'
    ];

    let sessionData: any = null;
    let accessToken: string | null = null;

    // Try to find session data in cookies
    for (const cookieName of cookieNames) {
      const cookieValue = cookies.get(cookieName)?.value;
      if (cookieValue) {
        try {
          // Try to parse as JSON first (for full session data)
          const parsed = JSON.parse(cookieValue);
          if (parsed.access_token) {
            sessionData = parsed;
            accessToken = parsed.access_token;
            break;
          }
        } catch {
          // If not JSON, treat as raw token
          accessToken = cookieValue;
        }
      }
    }

    if (!accessToken) {
      return null;
    }

    // Parse JWT to extract user information
    const jwtPayload: JwtPayload | null = parseJwtPayload(accessToken);
    if (!jwtPayload) {
      return null;
    }

    // Check if token is expired
    if (jwtPayload.exp && jwtPayload.exp * 1000 < Date.now()) {
      return null;
    }

    // Construct unified session from JWT claims
    const unifiedSession: UnifiedSession = {
      user: {
        id: jwtPayload.sub,
        email: jwtPayload.email,
        app_metadata: {
          roles: jwtPayload.role ? [jwtPayload.role] : [],
          tenant_id: jwtPayload.tenant_id,
        },
        user_metadata: {},
      },
      access_token: accessToken,
      refresh_token: sessionData?.refresh_token,
      expires_at: jwtPayload.exp,
      expires_in: jwtPayload.exp ? jwtPayload.exp - Math.floor(Date.now() / 1000) : undefined,
      token_type: 'bearer',
      tenant_id: jwtPayload.tenant_id,
      role: jwtPayload.role,
      source: 'legacy',
    };

    return unifiedSession;
  } catch (error) {
    console.error('Error extracting legacy session from cookies:', error);
    return null;
  }
}

/**
 * Supabase session extraction
 * Uses the official Supabase client to get session data
 */
async function supabaseGetSession(): Promise<UnifiedSession | null> {
  try {
    const supabase = await createServerClientForUser();
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error || !session) {
      return null;
    }

    // Parse JWT to get additional claims
    const jwtPayload: JwtPayload | null = parseJwtPayload(session.access_token);

    // Convert Supabase session to unified format
    const unifiedSession: UnifiedSession = {
      user: {
        id: session.user.id,
        email: session.user.email,
        app_metadata: session.user.app_metadata || {},
        user_metadata: session.user.user_metadata || {},
      },
      access_token: session.access_token,
      refresh_token: session.refresh_token,
      expires_at: session.expires_at,
      expires_in: session.expires_in,
      token_type: session.token_type,
      tenant_id: jwtPayload?.tenant_id,
      role: jwtPayload?.role,
      source: 'supabase',
    };

    return unifiedSession;
  } catch (error) {
    console.error('Error getting Supabase session:', error);
    return null;
  }
}

/**
 * Main unified session getter
 *
 * This is the primary function that components and middleware should use.
 * It automatically switches between Supabase and legacy auth based on the feature flag.
 *
 * @param cookies - Request cookies (for server-side usage)
 * @returns Unified session object or null if not authenticated
 */
export async function getUnifiedSession(cookies?: CookiesLike): Promise<UnifiedSession | null> {
  const useSupabase = process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH === 'true';

  try {
    if (useSupabase) {
      // Try Supabase first
      const supabaseSession = await supabaseGetSession();
      if (supabaseSession) {
        return supabaseSession;
      }
      
      // If Supabase fails and we have cookies, try legacy as fallback
      if (cookies) {
        console.warn('Supabase session not found, falling back to legacy auth');
        return await legacyGetSessionFromCookie(cookies);
      }
    } else {
      // Use legacy auth when flag is false
      if (cookies) {
        return await legacyGetSessionFromCookie(cookies);
      }
    }

    return null;
  } catch (error) {
    console.error('Error in getUnifiedSession:', error);
    return null;
  }
}

/**
 * Browser-specific session getter
 * For use in client-side components
 */
export async function getUnifiedSessionBrowser(): Promise<UnifiedSession | null> {
  const useSupabase = process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH === 'true';

  if (useSupabase) {
    return await supabaseGetSession();
  }

  // For legacy browser auth, we'd need to implement cookie reading
  // For now, return null and let components handle this case
  console.warn('Legacy browser session not implemented yet');
  return null;
}

/**
 * Utility to check if a session is valid (not expired)
 */
export function isSessionValid(session: UnifiedSession | null): boolean {
  if (!session) return false;
  
  if (session.expires_at && session.expires_at * 1000 < Date.now()) {
    return false;
  }
  
  return true;
}

/**
 * Utility to extract user roles from session
 */
export function getUserRoles(session: UnifiedSession | null): string[] {
  if (!session) return [];
  
  // Try app_metadata first (Supabase standard)
  if (session.user.app_metadata?.roles) {
    return Array.isArray(session.user.app_metadata.roles) 
      ? session.user.app_metadata.roles 
      : [session.user.app_metadata.roles];
  }
  
  // Fallback to role field (legacy)
  if (session.role) {
    return [session.role];
  }
  
  return [];
}
