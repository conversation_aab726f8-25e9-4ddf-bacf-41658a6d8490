'use client'

import { useUser } from '@/contexts/UserContext'

/**
 * Custom hook for role-based access control
 * Consistently uses the UserContext which ensures JWT claims are the source of truth
 */
export function useRbac() {
  const { role, tenantId, loading, jwtPayload } = useUser()

  return {
    /**
     * Check if the current user has any of the specified roles
     */
    hasRole: (roles: string | string[]): boolean => {
      if (loading) return false
      if (!role) return false

      const rolesToCheck = Array.isArray(roles) ? roles : [roles]
      return rolesToCheck.includes(role)
    },

    /**
     * Check if the current user is an admin (partner role)
     */
    isAdmin: (): boolean => {
      return role === 'partner'
    },

    /**
     * Check if the current user is a regular staff member
     */
    isStaff: (): boolean => {
      return ['partner', 'attorney', 'paralegal', 'staff'].includes(role || '')
    },

    /**
     * Check if the current user is a client
     */
    isClient: (): boolean => {
      return role === 'client'
    },

    /**
     * Check if the current user is a super admin
     */
    isSuperAdmin: (): boolean => {
      if (loading) return false
      return jwtPayload?.is_super_admin === true
    },

    /**
     * Check if the current user has super admin access
     * Alias for isSuperAdmin for convenience
     */
    hasSuperAdminAccess: (): boolean => {
      if (loading) return false
      return jwtPayload?.is_super_admin === true
    },

    /**
     * Get the current user's role
     */
    getRole: (): string | null => {
      return role
    },

    /**
     * Get the current tenant ID
     */
    getTenantId: (): string | null => {
      return tenantId
    },

    /**
     * Check if the user data is still loading
     */
    isLoading: (): boolean => {
      return loading
    },

    /**
     * Check if the user has access to a specific feature
     * @param feature The feature to check access for
     */
    hasAccess: (feature: 'templates' | 'documents' | 'cases' | 'admin'): boolean => {
      if (loading || !role) return false

      switch (feature) {
        case 'templates':
          return ['partner', 'attorney', 'staff'].includes(role)
        case 'documents':
          return ['partner', 'attorney', 'paralegal', 'staff', 'client'].includes(role)
        case 'cases':
          return ['partner', 'attorney', 'paralegal', 'staff'].includes(role)
        case 'admin':
          return role === 'partner'
        default:
          return false
      }
    },

    /**
     * Get all JWT claims for debugging
     */
    getJwtClaims: () => {
      return jwtPayload
    }
  }
}
