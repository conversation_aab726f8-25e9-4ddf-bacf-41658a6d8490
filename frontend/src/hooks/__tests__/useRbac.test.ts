/**
 * Tests for useRbac hook with super admin functionality
 */

import { renderHook } from '@testing-library/react';
import { useRbac } from '../useRbac';
import { useUser } from '@/contexts/UserContext';

// Mock the useUser hook
jest.mock('@/contexts/UserContext', () => ({
  useUser: jest.fn(),
}));

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;

describe('useRbac', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('isSuperAdmin', () => {
    it('should return false when loading', () => {
      mockUseUser.mockReturnValue({
        user: null,
        loading: true,
        role: null,
        tenantId: null,
        refreshUser: jest.fn(),
        jwtPayload: null,
      });

      const { result } = renderHook(() => useRbac());
      expect(result.current.isSuperAdmin()).toBe(false);
    });

    it('should return false when jwtPayload is null', () => {
      mockUseUser.mockReturnValue({
        user: null,
        loading: false,
        role: 'partner',
        tenantId: 'test-tenant',
        refreshUser: jest.fn(),
        jwtPayload: null,
      });

      const { result } = renderHook(() => useRbac());
      expect(result.current.isSuperAdmin()).toBe(false);
    });

    it('should return false when is_super_admin is false', () => {
      mockUseUser.mockReturnValue({
        user: { id: 'test-user' } as any,
        loading: false,
        role: 'partner',
        tenantId: 'test-tenant',
        refreshUser: jest.fn(),
        jwtPayload: {
          sub: 'test-user',
          exp: Date.now() / 1000 + 3600,
          role: 'partner',
          tenant_id: 'test-tenant',
          is_super_admin: false,
        },
      });

      const { result } = renderHook(() => useRbac());
      expect(result.current.isSuperAdmin()).toBe(false);
    });

    it('should return false when is_super_admin is undefined', () => {
      mockUseUser.mockReturnValue({
        user: { id: 'test-user' } as any,
        loading: false,
        role: 'partner',
        tenantId: 'test-tenant',
        refreshUser: jest.fn(),
        jwtPayload: {
          sub: 'test-user',
          exp: Date.now() / 1000 + 3600,
          role: 'partner',
          tenant_id: 'test-tenant',
          // is_super_admin is undefined
        },
      });

      const { result } = renderHook(() => useRbac());
      expect(result.current.isSuperAdmin()).toBe(false);
    });

    it('should return true when is_super_admin is true', () => {
      mockUseUser.mockReturnValue({
        user: { id: 'test-user' } as any,
        loading: false,
        role: 'partner',
        tenantId: 'test-tenant',
        refreshUser: jest.fn(),
        jwtPayload: {
          sub: 'test-user',
          exp: Date.now() / 1000 + 3600,
          role: 'partner',
          tenant_id: 'test-tenant',
          is_super_admin: true,
        },
      });

      const { result } = renderHook(() => useRbac());
      expect(result.current.isSuperAdmin()).toBe(true);
    });
  });

  describe('hasSuperAdminAccess', () => {
    it('should return the same value as isSuperAdmin', () => {
      mockUseUser.mockReturnValue({
        user: { id: 'test-user' } as any,
        loading: false,
        role: 'partner',
        tenantId: 'test-tenant',
        refreshUser: jest.fn(),
        jwtPayload: {
          sub: 'test-user',
          exp: Date.now() / 1000 + 3600,
          role: 'partner',
          tenant_id: 'test-tenant',
          is_super_admin: true,
        },
      });

      const { result } = renderHook(() => useRbac());
      expect(result.current.hasSuperAdminAccess()).toBe(true);
      expect(result.current.hasSuperAdminAccess()).toBe(result.current.isSuperAdmin());
    });

    it('should return false when loading', () => {
      mockUseUser.mockReturnValue({
        user: null,
        loading: true,
        role: null,
        tenantId: null,
        refreshUser: jest.fn(),
        jwtPayload: null,
      });

      const { result } = renderHook(() => useRbac());
      expect(result.current.hasSuperAdminAccess()).toBe(false);
    });
  });

  describe('existing functionality', () => {
    it('should maintain existing isAdmin functionality', () => {
      mockUseUser.mockReturnValue({
        user: { id: 'test-user' } as any,
        loading: false,
        role: 'partner',
        tenantId: 'test-tenant',
        refreshUser: jest.fn(),
        jwtPayload: {
          sub: 'test-user',
          exp: Date.now() / 1000 + 3600,
          role: 'partner',
          tenant_id: 'test-tenant',
        },
      });

      const { result } = renderHook(() => useRbac());
      expect(result.current.isAdmin()).toBe(true);
    });

    it('should maintain existing hasAccess functionality', () => {
      mockUseUser.mockReturnValue({
        user: { id: 'test-user' } as any,
        loading: false,
        role: 'partner',
        tenantId: 'test-tenant',
        refreshUser: jest.fn(),
        jwtPayload: {
          sub: 'test-user',
          exp: Date.now() / 1000 + 3600,
          role: 'partner',
          tenant_id: 'test-tenant',
        },
      });

      const { result } = renderHook(() => useRbac());
      expect(result.current.hasAccess('admin')).toBe(true);
    });
  });
});
