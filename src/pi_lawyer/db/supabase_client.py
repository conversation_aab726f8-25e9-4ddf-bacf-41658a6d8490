"""
Real Supabase client implementation.

This module provides a real Supabase client for production use.
"""

import logging
import os
from functools import lru_cache

# Setup logging
logger = logging.getLogger(__name__)

try:
    from supabase import Client, create_client
    SUPABASE_AVAILABLE = True
except ImportError:
    logger.warning("supabase-py not available, using mock client")
    SUPABASE_AVAILABLE = False

    # Mock client for when supabase-py is not available
    class Client:
        def __init__(self, *args, **kwargs):
            self.url = kwargs.get('url', 'mock-url')
            self.key = kwargs.get('key', 'mock-key')

        def table(self, table_name: str):
            return MockTable(table_name)

        def rpc(self, function_name: str, params: dict = None):
            return MockRPC(function_name, params)

    class MockTable:
        def __init__(self, table_name: str):
            self.table_name = table_name

        def select(self, columns: str = "*"):
            return MockQuery()

        def insert(self, data: dict):
            return MockQuery()

        def update(self, data: dict):
            return MockQuery()

        def delete(self):
            return MockQuery()

    class MockQuery:
        def eq(self, column: str, value):
            return self

        def execute(self):
            return {"data": [], "error": None}

    class MockRPC:
        def __init__(self, function_name: str, params: dict = None):
            self.function_name = function_name
            self.params = params

        def execute(self):
            return {"data": None, "error": None}

    def create_client(url: str, key: str, **kwargs):
        return Client(url=url, key=key, **kwargs)


@lru_cache(maxsize=1)
def get_supabase() -> Client:
    """
    Get a memoized Supabase client instance.

    Returns:
        Client: The Supabase client

    Raises:
        ValueError: If required environment variables are not set
    """
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_service_key = os.getenv("SUPABASE_SERVICE_KEY")

    if not supabase_url:
        raise ValueError("SUPABASE_URL environment variable is required")

    if not supabase_service_key:
        raise ValueError("SUPABASE_SERVICE_KEY environment variable is required")

    logger.info(
        f"Initializing Supabase client for URL: {supabase_url}"
    )

    return create_client(supabase_url, supabase_service_key)


# For backward compatibility
class SupabaseClient:
    """
    Wrapper class for backward compatibility.

    This provides the same interface as the old stub but uses the real client.
    """

    def __init__(self, *args, **kwargs):
        self._client = get_supabase()
        logger.info("Real SupabaseClient initialized")

    def authenticate(self, *args, **kwargs):
        """Authentication method - delegates to real client"""
        # Note: Service key clients don't need explicit authentication
        return {"authenticated": True}

    def get_user(self, user_id: str):
        """Get user by ID"""
        try:
            result = (
                self._client.table("auth.users")
                .select("*")
                .eq("id", user_id)
                .execute()
            )
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error getting user {user_id}: {e}")
            return None

    def get_data(self, table: str, filters: dict = None):
        """Get data from table"""
        try:
            query = self._client.table(table).select("*")
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)
            result = query.execute()
            return result.data
        except Exception as e:
            logger.error(f"Error getting data from {table}: {e}")
            return []

    def insert_data(self, table: str, data: dict):
        """Insert data into table"""
        try:
            result = self._client.table(table).insert(data).execute()
            return result.data[0] if result.data else {"id": "error"}
        except Exception as e:
            logger.error(f"Error inserting data into {table}: {e}")
            return {"id": "error"}
