"""
State Management for AiLex Agents

This module provides a comprehensive state management system for AiLex agents,
including a Pydantic + TypedDict bridge for state validation and serialization.

Key Features:
- TypedDict for runtime efficiency
- Pydantic model for validation and serialization
- Support for tenant isolation
- Thread management
- Memory operations
- Message handling
"""

import json
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, TypedDict, Union, cast

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from pydantic import BaseModel, Field, model_validator


class AiLexState(TypedDict, total=False):
    """
    TypedDict representation of the AiLex state.
    
    This is used for efficient runtime state management and is the primary
    state representation used by LangGraph.
    """
    tenant_id: str
    user_id: str
    agent: str
    page_intent: Optional[str]
    matter_id: Optional[str]
    locale: str
    active_doc: Optional[str]
    messages: List[Dict[str, Any]]
    memory: Dict[str, Any]
    async_job_id: Optional[str]
    created_at: str
    updated_at: str
    version: str
    thread_id: str


class StateModel(BaseModel):
    """
    Pydantic model for AiLex state.
    
    This model provides validation, serialization, and utility methods
    for working with AiLex state.
    """
    tenant_id: str = Field(..., description="The tenant ID")
    user_id: str = Field(..., description="The user ID")
    agent: str = Field(..., description="The current agent type")
    page_intent: Optional[str] = Field(None, description="The page intent from the UI")
    matter_id: Optional[str] = Field(None, description="The current matter ID")
    locale: str = Field("en", description="The locale for the user")
    active_doc: Optional[str] = Field(None, description="The ID of the active document")
    messages: List[Dict[str, Any]] = Field(default_factory=list, description="The message history")
    memory: Dict[str, Any] = Field(default_factory=dict, description="The agent memory")
    async_job_id: Optional[str] = Field(None, description="The ID of the current async job")
    created_at: str = Field(default_factory=lambda: datetime.now(timezone.utc).isoformat(), description="When the state was created")
    updated_at: str = Field(default_factory=lambda: datetime.now(timezone.utc).isoformat(), description="When the state was last updated")
    version: str = Field("1.0.0", description="The state schema version")
    thread_id: str = Field(..., description="The thread ID")

    @model_validator(mode="after")
    def update_timestamps(self) -> "StateModel":
        """Update the timestamps when the model is validated."""
        self.updated_at = datetime.now(timezone.utc).isoformat()
        return self

    def to_typed_dict(self) -> AiLexState:
        """Convert the Pydantic model to a TypedDict."""
        return cast(AiLexState, self.model_dump())

    def to_json(self) -> str:
        """Convert the state to a JSON string."""
        return json.dumps(self.model_dump())

    @classmethod
    def from_json(cls, json_str: str) -> "StateModel":
        """Create a state object from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_typed_dict(cls, state_dict: AiLexState) -> "StateModel":
        """Create a state object from a TypedDict."""
        return cls(**state_dict)

    def add_message(self, role: str, content: Union[str, List[Dict[str, Any]]]) -> None:
        """
        Add a message to the message history.
        
        Args:
            role: The role of the message sender (user, assistant, system)
            content: The content of the message
        """
        self.messages.append({
            "role": role,
            "content": content,
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def get_messages_as_langchain(self) -> List[BaseMessage]:
        """
        Get the messages as LangChain message objects.
        
        Returns:
            A list of LangChain message objects
        """
        result = []
        for message in self.messages:
            if message["role"] == "user":
                result.append(HumanMessage(content=message["content"]))
            elif message["role"] == "assistant":
                result.append(AIMessage(content=message["content"]))
            elif message["role"] == "system":
                result.append(SystemMessage(content=message["content"]))
        return result

    def set_memory(self, key: str, value: Any) -> None:
        """
        Set a value in the agent memory.
        
        Args:
            key: The memory key
            value: The memory value
        """
        self.memory[key] = value
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def get_memory(self, key: str, default: Any = None) -> Any:
        """
        Get a value from the agent memory.
        
        Args:
            key: The memory key
            default: The default value to return if the key is not found
            
        Returns:
            The memory value or the default value
        """
        return self.memory.get(key, default)


def create_state(
    tenant_id: str,
    user_id: str,
    thread_id: str,
    agent_type: str,
    matter_id: Optional[str] = None,
    locale: str = "en",
    active_doc: Optional[str] = None,
    page_intent: Optional[str] = None,
    initial_memory: Optional[Dict[str, Any]] = None,
    initial_messages: Optional[List[Dict[str, Any]]] = None
) -> AiLexState:
    """
    Create a new state object.
    
    Args:
        tenant_id: The tenant ID
        user_id: The user ID
        thread_id: The thread ID
        agent_type: The agent type
        matter_id: The matter ID (optional)
        locale: The locale (default: "en")
        active_doc: The active document ID (optional)
        page_intent: The page intent from the UI (optional)
        initial_memory: Initial memory (optional)
        initial_messages: Initial messages (optional)
        
    Returns:
        A new AiLexState object
    """
    state_model = StateModel(
        tenant_id=tenant_id,
        user_id=user_id,
        thread_id=thread_id,
        agent=agent_type,
        matter_id=matter_id,
        locale=locale,
        active_doc=active_doc,
        page_intent=page_intent,
        memory=initial_memory or {},
        messages=initial_messages or []
    )
    return state_model.to_typed_dict()


def create_typed_state(state_dict: AiLexState) -> AiLexState:
    """
    Create a validated state object from a TypedDict.
    
    This function validates the state using the Pydantic model and returns
    a TypedDict that can be used with LangGraph.
    
    Args:
        state_dict: The state dictionary
        
    Returns:
        A validated AiLexState object
    """
    state_model = StateModel.from_typed_dict(state_dict)
    return state_model.to_typed_dict()


def add_messages(state: AiLexState, messages: List[Dict[str, Any]]) -> AiLexState:
    """
    Add messages to the state.
    
    Args:
        state: The current state
        messages: The messages to add
        
    Returns:
        The updated state
    """
    state_model = StateModel.from_typed_dict(state)
    for message in messages:
        state_model.add_message(message["role"], message["content"])
    return state_model.to_typed_dict()
