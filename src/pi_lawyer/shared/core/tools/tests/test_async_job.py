"""
Tests for the Async Job Tool

This module implements tests for the async job tool.
"""

from unittest.mock import MagicMock, patch

import pytest

from pi_lawyer.shared.core.tools.async_job import enqueue_async_job
from pi_lawyer.shared.core.tools.executor import get_tool_executor


@pytest.mark.asyncio
class TestAsyncJobTool:
    """Tests for the async job tool."""
    
    @pytest.fixture
    def mock_redis_queue(self):
        """Mock the Redis queue service."""
        with patch("pi_lawyer.shared.core.tools.async_job.get_redis_queue_service") as mock:
            # Configure the mock to return a valid response
            mock_instance = mock.return_value
            mock_instance.enqueue = MagicMock(return_value="job-123")
            yield mock_instance
    
    async def test_enqueue_async_job(self, mock_redis_queue):
        """Test enqueueing an async job."""
        # Call the function
        job_id = await enqueue_async_job(
            tool_name="documentDraftAgent",
            params={"template": "settlement_letter", "matter_id": "123"},
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789"
        )
        
        # Verify the job ID is a string
        assert isinstance(job_id, str)
        
        # Verify the Redis queue was called
        mock_redis_queue.enqueue.assert_called_once()
        
        # Verify the job data
        args = mock_redis_queue.enqueue.call_args[0][0]
        assert args["tool_name"] == "documentDraftAgent"
        assert args["params"]["template"] == "settlement_letter"
        assert args["params"]["matter_id"] == "123"
        assert args["tenant_id"] == "tenant-123"
        assert args["user_id"] == "user-456"
        assert args["thread_id"] == "thread-789"
    
    async def test_enqueue_async_job_with_redis_error(self):
        """Test enqueueing an async job with Redis error."""
        # Mock Redis to raise an exception
        with patch("pi_lawyer.shared.core.tools.async_job.get_redis_queue_service") as mock:
            mock.side_effect = Exception("Redis error")
            
            # Mock the in-memory queue
            with patch("pi_lawyer.shared.core.tools.async_job._enqueue_in_memory") as mock_in_memory:
                # Call the function
                job_id = await enqueue_async_job(
                    tool_name="documentDraftAgent",
                    params={"template": "settlement_letter", "matter_id": "123"},
                    tenant_id="tenant-123",
                    user_id="user-456",
                    thread_id="thread-789"
                )
                
                # Verify the job ID is a string
                assert isinstance(job_id, str)
                
                # Verify the in-memory queue was called
                mock_in_memory.assert_called_once()
                
                # Verify the job data
                args = mock_in_memory.call_args[0][0]
                assert args["tool_name"] == "documentDraftAgent"
                assert args["params"]["template"] == "settlement_letter"
                assert args["params"]["matter_id"] == "123"
                assert args["tenant_id"] == "tenant-123"
    
    async def test_tool_registration(self):
        """Test that the tool is registered with the executor."""
        # Get the tool executor
        executor = get_tool_executor()
        
        # Verify the tool is registered
        assert "enqueue_async_job" in executor.tools
        
        # Verify the tool function is correct
        assert executor.tools["enqueue_async_job"] == enqueue_async_job
