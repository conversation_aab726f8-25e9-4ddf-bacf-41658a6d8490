"""
Tools Module

This module provides JSON schema definitions and execution functionality
for tools used by AiLex agents.
"""

from .async_job import enqueue_async_job, register_async_job_tool
from .executor import ToolExecutionError, ToolExecutor, get_tool_executor
from .schema import TOOL_REGISTRY, get_tool_schema, get_tools_for_agent

# Register the async job tool
register_async_job_tool()

__all__ = [
    # Schema
    "get_tool_schema",
    "get_tools_for_agent",
    "TOOL_REGISTRY",

    # Executor
    "ToolExecutor",
    "ToolExecutionError",
    "get_tool_executor",

    # Async Job
    "enqueue_async_job",
    "register_async_job_tool"
]
