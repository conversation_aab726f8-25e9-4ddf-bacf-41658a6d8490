"""
Tool Schema Definitions

This module provides JSON schema definitions for tools used by AiLex agents.
"""

from typing import Any, Dict, List

# Database tools
DATABASE_QUERY_TOOL = {
    "type": "function",
    "function": {
        "name": "query_database",
        "description": "Query the database for information",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The SQL query to execute"
                },
                "tenant_id": {
                    "type": "string",
                    "description": "The tenant ID for the query"
                }
            },
            "required": ["query", "tenant_id"]
        }
    }
}

DATABASE_INSERT_TOOL = {
    "type": "function",
    "function": {
        "name": "insert_database",
        "description": "Insert data into the database",
        "parameters": {
            "type": "object",
            "properties": {
                "table": {
                    "type": "string",
                    "description": "The table to insert into"
                },
                "data": {
                    "type": "object",
                    "description": "The data to insert"
                },
                "tenant_id": {
                    "type": "string",
                    "description": "The tenant ID for the insert"
                }
            },
            "required": ["table", "data", "tenant_id"]
        }
    }
}

DATABASE_UPDATE_TOOL = {
    "type": "function",
    "function": {
        "name": "update_database",
        "description": "Update data in the database",
        "parameters": {
            "type": "object",
            "properties": {
                "table": {
                    "type": "string",
                    "description": "The table to update"
                },
                "data": {
                    "type": "object",
                    "description": "The data to update"
                },
                "where": {
                    "type": "object",
                    "description": "The where clause for the update"
                },
                "tenant_id": {
                    "type": "string",
                    "description": "The tenant ID for the update"
                }
            },
            "required": ["table", "data", "where", "tenant_id"]
        }
    }
}

# Document tools
DOCUMENT_SEARCH_TOOL = {
    "type": "function",
    "function": {
        "name": "search_documents",
        "description": "Search for documents using vector search",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The search query"
                },
                "tenant_id": {
                    "type": "string",
                    "description": "The tenant ID for the search"
                },
                "limit": {
                    "type": "integer",
                    "description": "The maximum number of results to return",
                    "default": 10
                },
                "filter": {
                    "type": "object",
                    "description": "Filters to apply to the search",
                    "default": {}
                }
            },
            "required": ["query", "tenant_id"]
        }
    }
}

DOCUMENT_UPLOAD_TOOL = {
    "type": "function",
    "function": {
        "name": "upload_document",
        "description": "Upload a document to the system",
        "parameters": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string",
                    "description": "The document content"
                },
                "title": {
                    "type": "string",
                    "description": "The document title"
                },
                "mime_type": {
                    "type": "string",
                    "description": "The document MIME type"
                },
                "tenant_id": {
                    "type": "string",
                    "description": "The tenant ID for the document"
                },
                "matter_id": {
                    "type": "string",
                    "description": "The matter ID for the document"
                }
            },
            "required": ["content", "title", "mime_type", "tenant_id"]
        }
    }
}

# Calendar tools
CALENDAR_CREATE_EVENT_TOOL = {
    "type": "function",
    "function": {
        "name": "create_calendar_event",
        "description": "Create a calendar event",
        "parameters": {
            "type": "object",
            "properties": {
                "title": {
                    "type": "string",
                    "description": "The event title"
                },
                "start_time": {
                    "type": "string",
                    "description": "The event start time (ISO 8601 format)"
                },
                "end_time": {
                    "type": "string",
                    "description": "The event end time (ISO 8601 format)"
                },
                "description": {
                    "type": "string",
                    "description": "The event description"
                },
                "location": {
                    "type": "string",
                    "description": "The event location"
                },
                "attendees": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "The event attendees (email addresses)"
                },
                "tenant_id": {
                    "type": "string",
                    "description": "The tenant ID for the event"
                },
                "user_id": {
                    "type": "string",
                    "description": "The user ID for the event"
                }
            },
            "required": ["title", "start_time", "end_time", "tenant_id", "user_id"]
        }
    }
}

# Task tools
TASK_CREATE_TOOL = {
    "type": "function",
    "function": {
        "name": "create_task",
        "description": "Create a task",
        "parameters": {
            "type": "object",
            "properties": {
                "title": {
                    "type": "string",
                    "description": "The task title"
                },
                "description": {
                    "type": "string",
                    "description": "The task description"
                },
                "due_date": {
                    "type": "string",
                    "description": "The task due date (ISO 8601 format)"
                },
                "priority": {
                    "type": "string",
                    "enum": ["low", "medium", "high"],
                    "description": "The task priority"
                },
                "assignee_id": {
                    "type": "string",
                    "description": "The ID of the user assigned to the task"
                },
                "matter_id": {
                    "type": "string",
                    "description": "The matter ID for the task"
                },
                "tenant_id": {
                    "type": "string",
                    "description": "The tenant ID for the task"
                }
            },
            "required": ["title", "tenant_id"]
        }
    }
}

# Email tools
EMAIL_DRAFT_TOOL = {
    "type": "function",
    "function": {
        "name": "draft_email",
        "description": "Draft an email",
        "parameters": {
            "type": "object",
            "properties": {
                "to": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "The email recipients"
                },
                "cc": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "The email CC recipients"
                },
                "subject": {
                    "type": "string",
                    "description": "The email subject"
                },
                "body": {
                    "type": "string",
                    "description": "The email body"
                },
                "tenant_id": {
                    "type": "string",
                    "description": "The tenant ID for the email"
                },
                "user_id": {
                    "type": "string",
                    "description": "The user ID for the email"
                }
            },
            "required": ["to", "subject", "body", "tenant_id", "user_id"]
        }
    }
}

# Async job tools
ASYNC_JOB_TOOL = {
    "type": "function",
    "function": {
        "name": "enqueue_async_job",
        "description": "Enqueue an async job",
        "parameters": {
            "type": "object",
            "properties": {
                "tool_name": {
                    "type": "string",
                    "description": "The name of the tool to execute asynchronously"
                },
                "params": {
                    "type": "object",
                    "description": "The parameters for the tool"
                },
                "tenant_id": {
                    "type": "string",
                    "description": "The tenant ID for the job"
                },
                "user_id": {
                    "type": "string",
                    "description": "The user ID for the job"
                },
                "thread_id": {
                    "type": "string",
                    "description": "The thread ID for the job"
                }
            },
            "required": ["tool_name", "params", "tenant_id", "user_id", "thread_id"]
        }
    }
}

# Tool registry
TOOL_REGISTRY = {
    # Database tools
    "query_database": DATABASE_QUERY_TOOL,
    "insert_database": DATABASE_INSERT_TOOL,
    "update_database": DATABASE_UPDATE_TOOL,
    
    # Document tools
    "search_documents": DOCUMENT_SEARCH_TOOL,
    "upload_document": DOCUMENT_UPLOAD_TOOL,
    
    # Calendar tools
    "create_calendar_event": CALENDAR_CREATE_EVENT_TOOL,
    
    # Task tools
    "create_task": TASK_CREATE_TOOL,
    
    # Email tools
    "draft_email": EMAIL_DRAFT_TOOL,
    
    # Async job tools
    "enqueue_async_job": ASYNC_JOB_TOOL
}


def get_tool_schema(tool_name: str) -> Dict[str, Any]:
    """
    Get the schema for a tool.
    
    Args:
        tool_name: The name of the tool
        
    Returns:
        The tool schema
        
    Raises:
        ValueError: If the tool is not found
    """
    if tool_name not in TOOL_REGISTRY:
        raise ValueError(f"Tool {tool_name} not found")
    
    return TOOL_REGISTRY[tool_name]


def get_tools_for_agent(agent_type: str) -> List[Dict[str, Any]]:
    """
    Get the tools for an agent.
    
    Args:
        agent_type: The agent type
        
    Returns:
        The tools for the agent
    """
    if agent_type == "intake":
        return [
            DATABASE_QUERY_TOOL,
            DATABASE_INSERT_TOOL,
            DATABASE_UPDATE_TOOL,
            TASK_CREATE_TOOL
        ]
    elif agent_type == "research":
        return [
            DOCUMENT_SEARCH_TOOL,
            ASYNC_JOB_TOOL
        ]
    elif agent_type == "document":
        return [
            DOCUMENT_SEARCH_TOOL,
            DOCUMENT_UPLOAD_TOOL
        ]
    elif agent_type == "deadline":
        return [
            CALENDAR_CREATE_EVENT_TOOL,
            TASK_CREATE_TOOL
        ]
    elif agent_type == "email":
        return [
            EMAIL_DRAFT_TOOL
        ]
    elif agent_type == "supervisor":
        return list(TOOL_REGISTRY.values())
    
    return []
