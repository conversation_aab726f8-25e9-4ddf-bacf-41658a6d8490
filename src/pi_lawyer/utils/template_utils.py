"""Enhanced template parsing and processing utilities for multi-state document management."""

import logging
import re
from typing import Any, Dict, List, Optional, Set

# Regular expressions for different types of variable patterns
SIMPLE_VAR_PATTERN = r"\{\{\s*([a-zA-Z0-9_]+)\s*\}\}"
CONDITIONAL_IF_PATTERN = r"\{%\s*if\s+([a-zA-Z0-9_]+)\s*%\}"
CONDITIONAL_ELIF_PATTERN = r"\{%\s*elif\s+([a-zA-Z0-9_]+)\s*%\}"
CONDITIONAL_ELSE_PATTERN = r"\{%\s*else\s*%\}"
CONDITIONAL_END_PATTERN = r"\{%\s*endif\s*%\}"
LOOP_START_PATTERN = r"\{%\s*for\s+([a-zA-Z0-9_]+)\s+in\s+([a-zA-Z0-9_]+)\s*%\}"
LOOP_END_PATTERN = r"\{%\s*endfor\s*%\}"
INCLUDE_PATTERN = r'\{%\s*include\s+"([a-zA-Z0-9_.-]+)"\s*%\}'

logger = logging.getLogger(__name__)


def extract_variables(template_text: str) -> Set[str]:
    """
    Extract all variables from a template, including those in conditional blocks and loops.

    Args:
        template_text: The text content of the template

    Returns:
        A set of variable names found in the template
    """
    variables = set()

    # Extract simple variables
    simple_vars = re.findall(SIMPLE_VAR_PATTERN, template_text)
    variables.update(simple_vars)

    # Extract conditional variables
    if_vars = re.findall(CONDITIONAL_IF_PATTERN, template_text)
    elif_vars = re.findall(CONDITIONAL_ELIF_PATTERN, template_text)
    variables.update(if_vars)
    variables.update(elif_vars)

    # Extract loop variables
    for loop_match in re.finditer(LOOP_START_PATTERN, template_text):
        # The second group is the collection variable
        collection_var = loop_match.group(2)
        variables.add(collection_var)

    return variables


def analyze_template_structure(template_text: str) -> Dict[str, Any]:
    """
    Analyze the structure of a template, validating syntax and identifying nested blocks.

    Args:
        template_text: The text content of the template

    Returns:
        Dictionary with analysis results including variables, conditionals, loops, and includes
    """
    result = {
        "variables": extract_variables(template_text),
        "conditionals": [],
        "loops": [],
        "includes": [],
        "errors": [],
    }

    # Check for balanced conditionals
    if_count = len(re.findall(CONDITIONAL_IF_PATTERN, template_text))
    endif_count = len(re.findall(CONDITIONAL_END_PATTERN, template_text))

    if if_count != endif_count:
        result["errors"].append(
            f"Unbalanced conditional blocks: {if_count} 'if' tags and {endif_count} 'endif' tags"
        )

    # Check for balanced loops
    for_count = len(re.findall(LOOP_START_PATTERN, template_text))
    endfor_count = len(re.findall(LOOP_END_PATTERN, template_text))

    if for_count != endfor_count:
        result["errors"].append(
            f"Unbalanced loop blocks: {for_count} 'for' tags and {endfor_count} 'endfor' tags"
        )

    # Extract include statements
    includes = re.findall(INCLUDE_PATTERN, template_text)
    result["includes"] = includes

    return result


def render_template(template_text: str, variables: Dict[str, Any]) -> str:
    """
    Render a template with the provided variables, processing conditional blocks and loops.

    Args:
        template_text: The text content of the template
        variables: Dictionary of variable names and their values

    Returns:
        The rendered template with variables replaced and conditional/loop blocks processed
    """
    # First, process any conditional blocks
    template_text = process_conditionals(template_text, variables)

    # Then process any loops
    template_text = process_loops(template_text, variables)

    # Finally, replace simple variables
    for var_name, var_value in variables.items():
        # Skip non-string values that would be processed by conditionals/loops
        if not isinstance(var_value, (str, int, float, bool)) or var_value is None:
            continue

        pattern = re.compile(r"\{\{\s*" + re.escape(var_name) + r"\s*\}\}")
        template_text = pattern.sub(str(var_value), template_text)

    return template_text


def process_conditionals(template_text: str, variables: Dict[str, Any]) -> str:
    """
    Process conditional blocks in the template.

    Args:
        template_text: The text content of the template
        variables: Dictionary of variable names and their values

    Returns:
        Template with conditional blocks processed
    """
    # This is a simplified implementation
    result = template_text
    if_matches = list(re.finditer(CONDITIONAL_IF_PATTERN, template_text))

    # Process each if block (note: this is simplified and doesn't handle nested conditions properly)
    for if_match in if_matches:
        if_var = if_match.group(1)
        if_start = if_match.start()
        if_end = if_match.end()

        # Find the matching endif
        endif_match = re.search(CONDITIONAL_END_PATTERN, template_text[if_end:])
        if not endif_match:
            continue  # Unbalanced if, skip it

        endif_start = if_end + endif_match.start()
        endif_end = if_end + endif_match.end()

        # Extract the content between if and endif
        conditional_content = template_text[if_end:endif_start]

        # Check if there's an else clause
        else_match = re.search(CONDITIONAL_ELSE_PATTERN, conditional_content)
        if else_match:
            else_start = else_match.start()
            else_end = else_match.end()
            if_content = conditional_content[:else_start]
            else_content = conditional_content[else_end:]
        else:
            if_content = conditional_content
            else_content = ""

        # Check if the condition is satisfied
        condition_satisfied = variables.get(if_var, False)

        if condition_satisfied:
            # Replace the entire if-endif block with the if content
            result = result.replace(template_text[if_start:endif_end], if_content)
        else:
            # Replace the entire if-endif block with the else content
            result = result.replace(template_text[if_start:endif_end], else_content)

    return result


def process_loops(template_text: str, variables: Dict[str, Any]) -> str:
    """
    Process loop blocks in the template.

    Args:
        template_text: The text content of the template
        variables: Dictionary of variable names and their values

    Returns:
        Template with loop blocks processed
    """
    # This is a simplified implementation
    result = template_text
    loop_matches = list(re.finditer(LOOP_START_PATTERN, template_text))

    for loop_match in loop_matches:
        item_var = loop_match.group(1)
        collection_var = loop_match.group(2)
        loop_start = loop_match.start()
        loop_end = loop_match.end()

        # Find the matching endfor
        endfor_match = re.search(LOOP_END_PATTERN, template_text[loop_end:])
        if not endfor_match:
            continue  # Unbalanced loop, skip it

        endfor_start = loop_end + endfor_match.start()
        endfor_end = loop_end + endfor_match.end()

        # Extract the content to repeat
        loop_content = template_text[loop_end:endfor_start]

        # Get the collection to iterate over
        collection = variables.get(collection_var, [])
        if not isinstance(collection, (list, tuple, set)):
            collection = []

        # Generate the repeated content
        repeated_content = ""
        for item in collection:
            # Create a copy of variables with the loop item
            loop_vars = variables.copy()
            loop_vars[item_var] = item

            # Replace variables in this iteration
            iteration_content = loop_content
            for var_name, var_value in loop_vars.items():
                if isinstance(var_value, (str, int, float, bool)) or var_value is None:
                    pattern = re.compile(r"\{\{\s*" + re.escape(var_name) + r"\s*\}\}")
                    iteration_content = pattern.sub(
                        str(var_value) if var_value is not None else "",
                        iteration_content,
                    )

            repeated_content += iteration_content

        # Replace the entire loop block with the repeated content
        result = result.replace(template_text[loop_start:endfor_end], repeated_content)

    return result


def categorize_template(template_metadata: Dict[str, Any]) -> Dict[str, str]:
    """
    Determine appropriate categorization for a template based on its metadata.

    Args:
        template_metadata: Dictionary containing template metadata

    Returns:
        Dictionary with state, practice area, and category
    """
    result = {"state": "General", "practice_area": "General", "category": "Other"}

    # Extract state
    state = template_metadata.get("state", "").strip()
    if state:
        result["state"] = state

    # Extract practice area
    practice_area = template_metadata.get("practice_area", "").strip()
    if practice_area:
        result["practice_area"] = practice_area
    elif "personal injury" in template_metadata.get("name", "").lower():
        result["practice_area"] = "Personal Injury"
    elif "family" in template_metadata.get("name", "").lower():
        result["practice_area"] = "Family Law"

    # Extract category
    category = template_metadata.get("category", "").strip()
    if category:
        result["category"] = category
    else:
        name = template_metadata.get("name", "").lower()
        description = template_metadata.get("description", "").lower()

        if any(
            term in name or term in description
            for term in ["intake", "engagement", "authorization"]
        ):
            result["category"] = "Client Intake & Engagement"
        elif any(
            term in name or term in description
            for term in ["demand", "settlement", "pre-litigation"]
        ):
            result["category"] = "Pre-Litigation"
        elif any(
            term in name or term in description
            for term in ["complaint", "petition", "discovery", "motion"]
        ):
            result["category"] = "Litigation"
        elif any(
            term in name or term in description
            for term in ["trial", "jury", "verdict", "appeal"]
        ):
            result["category"] = "Trial & Post-Trial"
        elif any(
            term in name or term in description
            for term in ["operation", "firm", "policy"]
        ):
            result["category"] = "General Operations"

    return result


async def fetch_template_from_supabase(
    template_id: str, supabase_client
) -> Dict[str, Any]:
    """
    Fetch a template from Supabase by ID.

    Args:
        template_id: The ID of the template to fetch
        supabase_client: The initialized Supabase client

    Returns:
        The template data or None if not found
    """
    try:
        response = (
            await supabase_client.table("legal_templates")
            .select("*")
            .eq("id", template_id)
            .execute()
        )
        if response.data and len(response.data) > 0:
            return response.data[0]
        return None
    except Exception as e:
        logger.error(f"Error fetching template: {str(e)}")
        return None


async def save_template_to_supabase(
    template_data: Dict[str, Any], supabase_client
) -> Dict[str, Any]:
    """
    Save a template to Supabase.

    Args:
        template_data: The template data to save
        supabase_client: The initialized Supabase client

    Returns:
        The saved template data or None if error
    """
    try:
        print(f"Attempting to save template: {template_data.get('name', 'Unknown')}")

        if "id" in template_data and template_data["id"]:
            # Update existing template
            print(f"Updating existing template with ID: {template_data['id']}")
            response = (
                supabase_client.table("legal_templates")
                .update(template_data)
                .eq("id", template_data["id"])
                .execute()
            )
        else:
            # Create new template
            print("Creating new template")
            response = (
                supabase_client.table("legal_templates").insert(template_data).execute()
            )

        print(
            f"Response data: {response.data if hasattr(response, 'data') else 'No data'}"
        )
        print(
            f"Response error: {response.error if hasattr(response, 'error') else 'No error'}"
        )

        if response.data and len(response.data) > 0:
            return response.data[0]
        return None
    except Exception as e:
        print(f"Exception in save_template_to_supabase: {str(e)}")
        import traceback

        print(traceback.format_exc())
        logger.error(f"Error saving template: {str(e)}")
        return None


def compile_template_sections(
    sections: List[Dict[str, Any]], variables: Dict[str, Any]
) -> str:
    """
    Compile template sections into a single document, applying variables.

    Args:
        sections: List of section objects with content
        variables: Variables to apply to the template

    Returns:
        The compiled document
    """
    compiled_content = ""

    # Sort sections by order
    sorted_sections = sorted(sections, key=lambda s: s.get("order", 0))

    for section in sorted_sections:
        # Check if section should be included based on conditionalDisplay
        conditional_display = section.get("conditionalDisplay")
        if conditional_display:
            # Simple evaluation of condition
            try:
                # Replace variables in condition
                condition = conditional_display
                for var_name, var_value in variables.items():
                    placeholder = f"{{{var_name}}}"
                    if isinstance(var_value, bool):
                        condition = condition.replace(
                            placeholder, str(var_value).lower()
                        )
                    elif isinstance(var_value, (int, float)):
                        condition = condition.replace(placeholder, str(var_value))
                    elif isinstance(var_value, str):
                        condition = condition.replace(placeholder, f"'{var_value}'")

                # If condition evaluates to False, skip this section
                if not eval(condition):
                    continue
            except Exception as e:
                logger.error(f"Error evaluating conditional display: {str(e)}")
                # Include section by default on error

        # Get section content
        content = section.get("content", "")

        # Render the content with variables
        rendered_content = render_template(content, variables)

        # Add to compiled document
        if rendered_content:
            if compiled_content:
                compiled_content += "\n\n"
            compiled_content += rendered_content

    return compiled_content


def get_template_by_state_and_type(
    state: str, practice_area: str, template_type: str, templates: List[Dict[str, Any]]
) -> Optional[Dict[str, Any]]:
    """
    Find a template matching the given state, practice area and template type.
    Falls back to general templates if state-specific one not found.

    Args:
        state: The state code/name
        practice_area: The practice area
        template_type: The type of template to find
        templates: List of template objects

    Returns:
        The matching template or None if not found
    """
    # First, try to find state-specific template
    for template in templates:
        if (
            template.get("state") == state
            and template.get("practice_area") == practice_area
            and template.get("name", "").lower() == template_type.lower()
        ):
            return template

    # Fall back to general template
    for template in templates:
        if (
            template.get("state") == "General"
            and template.get("practice_area") == practice_area
            and template.get("name", "").lower() == template_type.lower()
        ):
            return template

    return None
