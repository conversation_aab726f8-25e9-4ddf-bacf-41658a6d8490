"""
Tests for the shared state module.

This module contains tests for the shared state module, including
state creation, validation, and serialization.
"""


from pi_lawyer.shared.core.state import (
    StateModel,
    add_messages,
    create_state,
    create_typed_state,
)


class TestStateModel:
    """Tests for the StateModel class."""
    
    def test_create_state_model(self):
        """Test creating a state model."""
        state = StateModel(
            tenant_id="test_tenant",
            user_id="test_user",
            thread_id="test_thread",
            agent="intake"
        )
        
        assert state.tenant_id == "test_tenant"
        assert state.user_id == "test_user"
        assert state.thread_id == "test_thread"
        assert state.agent == "intake"
        assert state.locale == "en"
        assert state.messages == []
        assert state.memory == {}
        assert state.version == "1.0.0"
    
    def test_add_message(self):
        """Test adding a message to the state."""
        state = StateModel(
            tenant_id="test_tenant",
            user_id="test_user",
            thread_id="test_thread",
            agent="intake"
        )
        
        state.add_message("user", "Hello")
        
        assert len(state.messages) == 1
        assert state.messages[0]["role"] == "user"
        assert state.messages[0]["content"] == "Hello"
        assert "timestamp" in state.messages[0]
    
    def test_get_messages_as_langchain(self):
        """Test getting messages as LangChain message objects."""
        state = StateModel(
            tenant_id="test_tenant",
            user_id="test_user",
            thread_id="test_thread",
            agent="intake"
        )
        
        state.add_message("user", "Hello")
        state.add_message("assistant", "Hi there")
        state.add_message("system", "System message")
        
        messages = state.get_messages_as_langchain()
        
        assert len(messages) == 3
        assert messages[0].content == "Hello"
        assert messages[1].content == "Hi there"
        assert messages[2].content == "System message"
    
    def test_set_get_memory(self):
        """Test setting and getting memory values."""
        state = StateModel(
            tenant_id="test_tenant",
            user_id="test_user",
            thread_id="test_thread",
            agent="intake"
        )
        
        state.set_memory("key1", "value1")
        state.set_memory("key2", {"nested": "value"})
        
        assert state.get_memory("key1") == "value1"
        assert state.get_memory("key2") == {"nested": "value"}
        assert state.get_memory("key3") is None
        assert state.get_memory("key3", "default") == "default"
    
    def test_to_typed_dict(self):
        """Test converting to a TypedDict."""
        state = StateModel(
            tenant_id="test_tenant",
            user_id="test_user",
            thread_id="test_thread",
            agent="intake"
        )
        
        state.add_message("user", "Hello")
        state.set_memory("key1", "value1")
        
        typed_dict = state.to_typed_dict()
        
        assert typed_dict["tenant_id"] == "test_tenant"
        assert typed_dict["user_id"] == "test_user"
        assert typed_dict["thread_id"] == "test_thread"
        assert typed_dict["agent"] == "intake"
        assert typed_dict["locale"] == "en"
        assert len(typed_dict["messages"]) == 1
        assert typed_dict["messages"][0]["role"] == "user"
        assert typed_dict["memory"]["key1"] == "value1"
    
    def test_to_from_json(self):
        """Test converting to and from JSON."""
        state = StateModel(
            tenant_id="test_tenant",
            user_id="test_user",
            thread_id="test_thread",
            agent="intake"
        )
        
        state.add_message("user", "Hello")
        state.set_memory("key1", "value1")
        
        json_str = state.to_json()
        new_state = StateModel.from_json(json_str)
        
        assert new_state.tenant_id == state.tenant_id
        assert new_state.user_id == state.user_id
        assert new_state.thread_id == state.thread_id
        assert new_state.agent == state.agent
        assert new_state.locale == state.locale
        assert len(new_state.messages) == len(state.messages)
        assert new_state.messages[0]["role"] == state.messages[0]["role"]
        assert new_state.memory["key1"] == state.memory["key1"]


class TestStateFunctions:
    """Tests for the state functions."""
    
    def test_create_state(self):
        """Test creating a state."""
        state = create_state(
            tenant_id="test_tenant",
            user_id="test_user",
            thread_id="test_thread",
            agent_type="intake"
        )
        
        assert state["tenant_id"] == "test_tenant"
        assert state["user_id"] == "test_user"
        assert state["thread_id"] == "test_thread"
        assert state["agent"] == "intake"
        assert state["locale"] == "en"
        assert state["messages"] == []
        assert state["memory"] == {}
    
    def test_create_typed_state(self):
        """Test creating a typed state."""
        state = create_state(
            tenant_id="test_tenant",
            user_id="test_user",
            thread_id="test_thread",
            agent_type="intake"
        )
        
        typed_state = create_typed_state(state)
        
        assert typed_state["tenant_id"] == "test_tenant"
        assert typed_state["user_id"] == "test_user"
        assert typed_state["thread_id"] == "test_thread"
        assert typed_state["agent"] == "intake"
    
    def test_add_messages(self):
        """Test adding messages to a state."""
        state = create_state(
            tenant_id="test_tenant",
            user_id="test_user",
            thread_id="test_thread",
            agent_type="intake"
        )
        
        messages = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there"}
        ]
        
        new_state = add_messages(state, messages)
        
        assert len(new_state["messages"]) == 2
        assert new_state["messages"][0]["role"] == "user"
        assert new_state["messages"][0]["content"] == "Hello"
        assert new_state["messages"][1]["role"] == "assistant"
        assert new_state["messages"][1]["content"] == "Hi there"
