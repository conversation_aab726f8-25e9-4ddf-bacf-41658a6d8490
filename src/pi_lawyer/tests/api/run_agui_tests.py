"""
Simple script to run the AG-UI protocol model tests directly.

This script runs the AG-UI protocol model tests without using pytest,
avoiding issues with global fixtures.
"""

import json
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, field_validator, model_validator

# Define the AG-UI protocol models for testing

class AGUIEventType(str, Enum):
    """AG-UI Event Types."""
    START = "start"
    CONTENT = "content"
    TOOL_CALLS = "tool_calls"
    TOOL_RESULTS = "tool_results"
    ERROR = "error"
    DONE = "done"
    STATE_SNAPSHOT = "state_snapshot"
    STATE_DELTA = "state_delta"
    METADATA = "metadata"
    PING = "ping"

class AGUIErrorType(str, Enum):
    """AG-UI Error Types."""
    AUTHENTICATION_ERROR = "authentication_error"
    AUTHORIZATION_ERROR = "authorization_error"
    INVALID_REQUEST = "invalid_request"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    INTERNAL_SERVER_ERROR = "internal_server_error"
    AGENT_ERROR = "agent_error"
    TOOL_EXECUTION_ERROR = "tool_execution_error"
    TIMEOUT_ERROR = "timeout_error"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"

class AGUIToolCallFunction(BaseModel):
    """AG-UI Tool Call Function format."""
    name: str
    arguments: str

    @field_validator("arguments")
    def validate_arguments_json(cls, v):
        """Validate that arguments is valid JSON."""
        try:
            if v:
                json.loads(v)
            return v
        except json.JSONDecodeError:
            raise ValueError("Tool call arguments must be valid JSON")

class AGUIToolCall(BaseModel):
    """AG-UI Tool Call format."""
    id: str
    type: str = "function"
    function: AGUIToolCallFunction

class AGUIMessage(BaseModel):
    """AG-UI Message format."""
    role: str
    content: Union[str, List[str], None]
    name: Optional[str] = None
    tool_calls: Optional[List[AGUIToolCall]] = None
    tool_call_id: Optional[str] = None

    @field_validator("role")
    def validate_role(cls, v):
        """Validate that role is one of the allowed values."""
        allowed_roles = ["user", "assistant", "system", "tool", "function"]
        if v not in allowed_roles:
            raise ValueError(f"Role must be one of {allowed_roles}")
        return v

    @model_validator(mode='after')
    def validate_tool_call_id(self):
        """Validate that tool_call_id is present if role is 'tool'."""
        if self.role == "tool" and not self.tool_call_id:
            raise ValueError("tool_call_id is required when role is 'tool'")
        return self

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility

class AGUIRequest(BaseModel):
    """AG-UI Request format."""
    messages: List[AGUIMessage]
    agent: Optional[str] = None
    threadId: Optional[str] = None
    stream: bool = True
    model: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    tools: Optional[List[Dict[str, Any]]] = None
    tool_choice: Optional[Union[str, Dict[str, Any]]] = None
    user: Optional[str] = None
    shared_state: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

    @field_validator("temperature")
    def validate_temperature(cls, v):
        """Validate that temperature is between 0 and 2."""
        if v is not None and (v < 0 or v > 2):
            raise ValueError("Temperature must be between 0 and 2")
        return v

    @field_validator("max_tokens")
    def validate_max_tokens(cls, v):
        """Validate that max_tokens is positive."""
        if v is not None and v <= 0:
            raise ValueError("max_tokens must be positive")
        return v

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility

class AGUIResponse(BaseModel):
    """AG-UI Response format."""
    messages: List[AGUIMessage]
    done: bool = True
    threadId: str
    customData: Optional[Dict[str, Any]] = None
    usage: Optional[Dict[str, int]] = None
    model: Optional[str] = None

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility

class AGUIStreamEvent(BaseModel):
    """AG-UI Stream Event format."""
    type: AGUIEventType
    content: Optional[str] = None
    threadId: Optional[str] = None
    toolCalls: Optional[List[AGUIToolCall]] = None
    toolResults: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    errorType: Optional[AGUIErrorType] = None
    metadata: Optional[Dict[str, Any]] = None
    stateSnapshot: Optional[Dict[str, Any]] = None
    stateDelta: Optional[Dict[str, Any]] = None

    @model_validator(mode='after')
    def validate_event_fields(self):
        """Validate that the event has the correct fields for its type."""
        if self.type == AGUIEventType.CONTENT and self.content is None:
            raise ValueError("content is required for content events")
        if self.type == AGUIEventType.TOOL_CALLS and self.toolCalls is None:
            raise ValueError("toolCalls is required for tool_calls events")
        if self.type == AGUIEventType.TOOL_RESULTS and self.toolResults is None:
            raise ValueError("toolResults is required for tool_results events")
        if self.type == AGUIEventType.ERROR and self.error is None:
            raise ValueError("error is required for error events")
        if self.type == AGUIEventType.STATE_SNAPSHOT and self.stateSnapshot is None:
            raise ValueError("stateSnapshot is required for state_snapshot events")
        if self.type == AGUIEventType.STATE_DELTA and self.stateDelta is None:
            raise ValueError("stateDelta is required for state_delta events")
        if self.type == AGUIEventType.METADATA and self.metadata is None:
            raise ValueError("metadata is required for metadata events")
        return self

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility


# Tests for AG-UI protocol models
def test_agui_message_model():
    """Test the AGUIMessage model."""
    print("Testing AGUIMessage model...")
    
    # Test valid message
    message = AGUIMessage(role="user", content="Test message")
    assert message.role == "user"
    assert message.content == "Test message"

    # Test message with tool calls
    tool_call = AGUIToolCall(
        id="tool-1",
        type="function",
        function=AGUIToolCallFunction(name="test_function", arguments="{}")
    )
    message = AGUIMessage(
        role="assistant",
        content="",
        tool_calls=[tool_call]
    )
    assert message.role == "assistant"
    assert message.tool_calls[0].id == "tool-1"

    # Test message with tool call ID
    message = AGUIMessage(
        role="tool",
        content="Tool result",
        tool_call_id="tool-1"
    )
    assert message.role == "tool"
    assert message.tool_call_id == "tool-1"

    # Test invalid role
    try:
        AGUIMessage(role="invalid_role", content="Test message")
        assert False, "Should have raised ValueError for invalid role"
    except ValueError:
        pass

    # Test missing tool_call_id for tool role
    try:
        AGUIMessage(role="tool", content="Tool result")
        assert False, "Should have raised ValueError for missing tool_call_id"
    except ValueError:
        pass
    
    print("✅ AGUIMessage model tests passed")


def test_agui_request_model():
    """Test the AGUIRequest model."""
    print("Testing AGUIRequest model...")
    
    # Test minimal request
    request = AGUIRequest(
        messages=[AGUIMessage(role="user", content="Test message")]
    )
    assert len(request.messages) == 1
    assert request.messages[0].role == "user"
    assert request.stream is True  # Default value

    # Test full request
    request = AGUIRequest(
        messages=[AGUIMessage(role="user", content="Test message")],
        agent="test-agent",
        threadId="test-thread",
        stream=False,
        model="gpt-4",
        temperature=0.7,
        max_tokens=100,
        tools=[{"type": "function", "function": {"name": "test_function", "parameters": {}}}],
        tool_choice="auto",
        user="test-user",
        shared_state={"key": "value"},
        metadata={"session_id": "123"}
    )
    assert request.agent == "test-agent"
    assert request.threadId == "test-thread"
    assert request.stream is False
    assert request.model == "gpt-4"
    assert request.temperature == 0.7
    assert request.max_tokens == 100
    assert len(request.tools) == 1
    assert request.tool_choice == "auto"
    assert request.user == "test-user"
    assert request.shared_state == {"key": "value"}
    assert request.metadata == {"session_id": "123"}

    # Test invalid temperature
    try:
        AGUIRequest(
            messages=[AGUIMessage(role="user", content="Test message")],
            temperature=3.0  # Invalid temperature (should be between 0 and 2)
        )
        assert False, "Should have raised ValueError for invalid temperature"
    except ValueError:
        pass

    # Test invalid max_tokens
    try:
        AGUIRequest(
            messages=[AGUIMessage(role="user", content="Test message")],
            max_tokens=0  # Invalid max_tokens (should be positive)
        )
        assert False, "Should have raised ValueError for invalid max_tokens"
    except ValueError:
        pass
    
    print("✅ AGUIRequest model tests passed")


def test_agui_response_model():
    """Test the AGUIResponse model."""
    print("Testing AGUIResponse model...")
    
    # Test minimal response
    response = AGUIResponse(
        messages=[AGUIMessage(role="assistant", content="Test response")],
        threadId="test-thread"
    )
    assert len(response.messages) == 1
    assert response.messages[0].role == "assistant"
    assert response.done is True  # Default value

    # Test full response
    response = AGUIResponse(
        messages=[AGUIMessage(role="assistant", content="Test response")],
        threadId="test-thread",
        done=False,
        customData={"key": "value"},
        usage={"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30},
        model="gpt-4"
    )
    assert response.done is False
    assert response.customData == {"key": "value"}
    assert response.usage == {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30}
    assert response.model == "gpt-4"
    
    print("✅ AGUIResponse model tests passed")


def test_agui_stream_event_model():
    """Test the AGUIStreamEvent model."""
    print("Testing AGUIStreamEvent model...")
    
    # Test start event
    event = AGUIStreamEvent(
        type=AGUIEventType.START,
        threadId="test-thread"
    )
    assert event.type == AGUIEventType.START
    assert event.threadId == "test-thread"

    # Test content event
    event = AGUIStreamEvent(
        type=AGUIEventType.CONTENT,
        content="Test content"
    )
    assert event.type == AGUIEventType.CONTENT
    assert event.content == "Test content"

    # Test tool calls event
    tool_call = AGUIToolCall(
        id="tool-1",
        type="function",
        function=AGUIToolCallFunction(name="test_function", arguments="{}")
    )
    event = AGUIStreamEvent(
        type=AGUIEventType.TOOL_CALLS,
        toolCalls=[tool_call]
    )
    assert event.type == AGUIEventType.TOOL_CALLS
    assert event.toolCalls[0].id == "tool-1"

    # Test error event
    event = AGUIStreamEvent(
        type=AGUIEventType.ERROR,
        error="Test error",
        errorType=AGUIErrorType.INTERNAL_SERVER_ERROR
    )
    assert event.type == AGUIEventType.ERROR
    assert event.error == "Test error"
    assert event.errorType == AGUIErrorType.INTERNAL_SERVER_ERROR

    # Test done event
    event = AGUIStreamEvent(
        type=AGUIEventType.DONE,
        threadId="test-thread"
    )
    assert event.type == AGUIEventType.DONE
    assert event.threadId == "test-thread"

    # Test missing required field for content event
    try:
        AGUIStreamEvent(type=AGUIEventType.CONTENT)
        assert False, "Should have raised ValueError for missing content"
    except ValueError:
        pass

    # Test missing required field for tool calls event
    try:
        AGUIStreamEvent(type=AGUIEventType.TOOL_CALLS)
        assert False, "Should have raised ValueError for missing toolCalls"
    except ValueError:
        pass

    # Test missing required field for error event
    try:
        AGUIStreamEvent(type=AGUIEventType.ERROR)
        assert False, "Should have raised ValueError for missing error"
    except ValueError:
        pass
    
    print("✅ AGUIStreamEvent model tests passed")


def run_all_tests():
    """Run all tests."""
    print("Running AG-UI protocol model tests...")
    print("====================================")
    
    test_agui_message_model()
    test_agui_request_model()
    test_agui_response_model()
    test_agui_stream_event_model()
    
    print("====================================")
    print("All AG-UI protocol model tests passed! ✅")


if __name__ == "__main__":
    run_all_tests()
