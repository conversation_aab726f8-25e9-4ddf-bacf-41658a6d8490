"""
Custom test configuration for the endpoint key verification tests.

This module contains fixtures and configuration for the endpoint key verification tests.
It overrides the global fixtures to avoid dependencies on the existing codebase.
"""

from unittest.mock import MagicMock, patch

import pytest


# Mock environment variables
@pytest.fixture(autouse=True)
def mock_environment_variables(monkeypatch):
    """Mock environment variables for testing."""
    monkeypatch.setenv("VOYAGE_API_KEY", "test-voyage-api-key")
    monkeypatch.setenv("PINECONE_API_KEY", "test-pinecone-api-key")
    monkeypatch.setenv("PINECONE_ENVIRONMENT", "test-environment")
    monkeypatch.setenv("PINECONE_INDEX_NAME", "test-index")
    monkeypatch.setenv("OPENAI_API_KEY", "test-openai-api-key")
    monkeypatch.setenv("SUPABASE_URL", "https://test.supabase.co")
    monkeypatch.setenv("SUPABASE_KEY", "test-supabase-key")
    monkeypatch.setenv("SUPABASE_JWT_SECRET", "test-jwt-secret")

# Mock the LangGraph agents
@pytest.fixture(autouse=True)
def mock_langgraph_agents():
    """
    Mock the LangGraph agents.
    
    This fixture is automatically used in all tests.
    """
    # Create mock objects for the agent apps
    mock_intake_graph = MagicMock()
    mock_document_graph = MagicMock()
    mock_research_graph = MagicMock()
    mock_event_graph = MagicMock()
    mock_deadline_graph = MagicMock()
    
    # Configure the mocks
    mock_intake_graph.invoke.return_value = {"response": "This is a test response"}
    mock_document_graph.invoke.return_value = {"response": "This is a test response"}
    mock_research_graph.invoke.return_value = {"response": "This is a test response"}
    mock_event_graph.invoke.return_value = {"response": "This is a test response"}
    mock_deadline_graph.invoke.return_value = {"response": "This is a test response"}
    
    # Patch the agent modules
    with patch.dict("sys.modules", {
        "src.pi_lawyer.agents.intake_agent": MagicMock(app=mock_intake_graph),
        "src.pi_lawyer.agents.document_agent": MagicMock(app=mock_document_graph),
        "src.pi_lawyer.agents.research_agent": MagicMock(app=mock_research_graph),
        "src.pi_lawyer.agents.event_agent": MagicMock(app=mock_event_graph),
        "src.pi_lawyer.agents.deadline_agent": MagicMock(app=mock_deadline_graph),
    }):
        yield {
            "intake_graph": mock_intake_graph,
            "document_graph": mock_document_graph,
            "research_graph": mock_research_graph,
            "event_graph": mock_event_graph,
            "deadline_graph": mock_deadline_graph,
        }
