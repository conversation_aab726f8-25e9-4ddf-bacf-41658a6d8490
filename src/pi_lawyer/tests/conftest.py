"""
Test configuration for the PI Lawyer AI application.

This module contains fixtures and configuration for tests.
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest


@pytest.fixture(autouse=True)
def mock_environment_variables(monkeypatch):
    """
    Mock environment variables for testing.

    This fixture is automatically used in all tests.
    """
    # Set environment variables for testing
    monkeypatch.setenv("VOYAGE_API_KEY", "test-voyage-api-key")
    monkeypatch.setenv("PINECONE_API_KEY", "test-pinecone-api-key")
    monkeypatch.setenv("PINECONE_ENVIRONMENT", "test-environment")
    monkeypatch.setenv("PINECONE_INDEX_NAME", "test-index")
    monkeypatch.setenv("OPENAI_API_KEY", "test-openai-api-key")
    monkeypatch.setenv("SUPABASE_URL", "https://test.supabase.co")
    monkeypatch.setenv("SUPABASE_KEY", "test-supabase-key")
    monkeypatch.setenv("SUPABASE_JWT_SECRET", "test-jwt-secret")
    monkeypatch.setenv("CPK_ENDPOINT_SECRET", "test-endpoint-secret")


@pytest.fixture(autouse=True)
def mock_voyage_embeddings():
    """
    Mock the VoyageAIEmbeddings class.

    This fixture is automatically used in all tests.
    """
    with patch("pi_lawyer.utils.voyage_embeddings.VoyageAIEmbeddings") as mock:
        # Configure the mock to return a fixed embedding
        mock_instance = MagicMock()
        mock_instance.embed_documents.return_value = [[0.1, 0.2, 0.3] for _ in range(10)]
        mock_instance.embed_query.return_value = [0.1, 0.2, 0.3]
        mock.return_value = mock_instance
        yield mock


@pytest.fixture(autouse=True)
def mock_pinecone_client():
    """
    Mock the PineconeClient class.

    This fixture is automatically used in all tests.
    """
    with patch("pi_lawyer.db.pinecone_client.Pinecone") as mock_pinecone:
        # Configure the mock to return a fixed index
        mock_index = MagicMock()
        mock_index.query.return_value = {
            "matches": [
                {"id": "doc1", "score": 0.9, "metadata": {"text": "Test document 1"}},
                {"id": "doc2", "score": 0.8, "metadata": {"text": "Test document 2"}},
            ]
        }
        mock_pinecone_instance = MagicMock()
        mock_pinecone_instance.Index.return_value = mock_index
        mock_pinecone.return_value = mock_pinecone_instance
        yield mock_pinecone


@pytest.fixture(autouse=True)
def mock_supabase_client():
    """
    Mock the SupabaseClient class.

    This fixture is automatically used in all tests.
    """
    with patch("pi_lawyer.db.supabase_client.SupabaseClient") as mock:
        # Configure the mock to return fixed data
        mock_instance = MagicMock()
        mock_instance.get_user.return_value = {"id": "test-user-id", "email": "<EMAIL>"}
        mock_instance.get_tenant.return_value = {"id": "test-tenant-id", "name": "Test Tenant"}
        mock.return_value = mock_instance
        yield mock


@pytest.fixture(autouse=True)
def mock_langgraph_agents():
    """
    Mock the LangGraph agents.

    This fixture is automatically used in all tests.
    """
    with patch("pi_lawyer.agents.intake_agent.app") as mock_intake_graph, \
         patch("pi_lawyer.agents.document_agent.app") as mock_document_graph, \
         patch("pi_lawyer.agents.research_agent.app") as mock_research_graph, \
         patch("pi_lawyer.agents.event_agent.app") as mock_event_graph, \
         patch("pi_lawyer.agents.deadline_agent.app") as mock_deadline_graph:

        # Configure the mocks to return fixed graphs
        mock_graph = MagicMock()
        mock_graph.ainvoke.return_value = {"response": "Test response"}

        mock_intake_graph.return_value = mock_graph
        mock_document_graph.return_value = mock_graph
        mock_research_graph.return_value = mock_graph
        mock_event_graph.return_value = mock_graph
        mock_deadline_graph.return_value = mock_graph

        yield {
            "intake_graph": mock_intake_graph,
            "document_graph": mock_document_graph,
            "research_graph": mock_research_graph,
            "event_graph": mock_event_graph,
            "deadline_graph": mock_deadline_graph,
        }


@pytest.fixture
def mock_env_endpoint_secret(monkeypatch):
    """Mock the CPK_ENDPOINT_SECRET environment variable."""
    monkeypatch.setenv("CPK_ENDPOINT_SECRET", "test-secret")


@pytest.fixture
def mock_env_no_endpoint_secret(monkeypatch):
    """Mock the CPK_ENDPOINT_SECRET environment variable to be empty."""
    monkeypatch.delenv("CPK_ENDPOINT_SECRET", raising=False)


@pytest.fixture(autouse=True)
async def mock_db_client():
    """
    Mock the database client.

    This fixture is automatically used in all tests.
    """
    with patch("pi_lawyer.db.client.get_db_client") as mock_get_db_client:
        # Create a mock client
        mock_client = AsyncMock()
        mock_table = AsyncMock()
        mock_select = AsyncMock()
        mock_eq = AsyncMock()
        mock_single = AsyncMock()
        mock_insert = AsyncMock()
        mock_update = AsyncMock()
        mock_delete = AsyncMock()
        mock_execute = AsyncMock()

        # Configure the mock chain
        mock_client.table.return_value = mock_table
        mock_table.select.return_value = mock_select
        mock_select.eq.return_value = mock_eq
        mock_eq.eq.return_value = mock_eq
        mock_eq.single.return_value = mock_single
        mock_table.insert.return_value = mock_insert
        mock_insert.execute.return_value = mock_execute
        mock_table.update.return_value = mock_update
        mock_update.eq.return_value = mock_eq
        mock_table.delete.return_value = mock_delete
        mock_delete.eq.return_value = mock_eq

        # Configure the mock to return the mock client
        mock_get_db_client.return_value = mock_client

        yield mock_get_db_client
