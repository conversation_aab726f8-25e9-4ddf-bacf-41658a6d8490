"""
Tests for the State Management module

This module contains tests for the state management implementation.
"""

from datetime import datetime

from src.pi_lawyer.state.state import <PERSON><PERSON><PERSON><PERSON><PERSON>, create_state


def test_state_initialization():
    """Test that the state initializes correctly."""
    state = AiLexState(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789"
    )
    
    assert state.tenant_id == "tenant-123"
    assert state.user_id == "user-456"
    assert state.thread_id == "thread-789"
    assert state.locale == "en"  # Default value
    assert state.matter_id is None
    assert state.active_doc is None
    assert state.memory == {}
    assert state.messages == []
    assert state.created_at is not None
    assert state.updated_at is not None
    
    # Verify timestamps are valid ISO format
    datetime.fromisoformat(state.created_at)
    datetime.fromisoformat(state.updated_at)

def test_create_state_factory():
    """Test the create_state factory function."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        matter_id="matter-101112",
        locale="fr",
        active_doc="doc-131415",
        initial_memory={"key": "value"},
        initial_messages=[{"role": "system", "content": "Hello"}]
    )
    
    assert state.tenant_id == "tenant-123"
    assert state.user_id == "user-456"
    assert state.thread_id == "thread-789"
    assert state.matter_id == "matter-101112"
    assert state.locale == "fr"
    assert state.active_doc == "doc-131415"
    assert state.memory == {"key": "value"}
    assert len(state.messages) == 1
    assert state.messages[0]["role"] == "system"
    assert state.messages[0]["content"] == "Hello"

def test_state_add_message():
    """Test adding messages to the state."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789"
    )
    
    # Add a message
    state.add_message("user", "Hello, world!")
    
    assert len(state.messages) == 1
    assert state.messages[0]["role"] == "user"
    assert state.messages[0]["content"] == "Hello, world!"
    assert "timestamp" in state.messages[0]
    
    # Add another message
    state.add_message("assistant", "How can I help you?")
    
    assert len(state.messages) == 2
    assert state.messages[1]["role"] == "assistant"
    assert state.messages[1]["content"] == "How can I help you?"

def test_state_get_last_message():
    """Test getting the last message from the state."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789"
    )
    
    # No messages yet
    assert state.get_last_message() is None
    
    # Add messages
    state.add_message("user", "Hello")
    state.add_message("assistant", "Hi there")
    
    # Get last message
    last_message = state.get_last_message()
    assert last_message is not None
    assert last_message["role"] == "assistant"
    assert last_message["content"] == "Hi there"

def test_state_get_messages_by_role():
    """Test getting messages by role."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789"
    )
    
    # Add messages with different roles
    state.add_message("system", "System message")
    state.add_message("user", "User message 1")
    state.add_message("assistant", "Assistant message 1")
    state.add_message("user", "User message 2")
    state.add_message("assistant", "Assistant message 2")
    
    # Get messages by role
    system_messages = state.get_messages_by_role("system")
    user_messages = state.get_messages_by_role("user")
    assistant_messages = state.get_messages_by_role("assistant")
    
    assert len(system_messages) == 1
    assert system_messages[0]["content"] == "System message"
    
    assert len(user_messages) == 2
    assert user_messages[0]["content"] == "User message 1"
    assert user_messages[1]["content"] == "User message 2"
    
    assert len(assistant_messages) == 2
    assert assistant_messages[0]["content"] == "Assistant message 1"
    assert assistant_messages[1]["content"] == "Assistant message 2"

def test_state_memory_operations():
    """Test memory operations in the state."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789"
    )
    
    # Initially empty
    assert state.memory == {}
    
    # Set a value
    state.set_memory("key1", "value1")
    assert state.memory["key1"] == "value1"
    
    # Get a value
    assert state.get_memory("key1") == "value1"
    
    # Get a non-existent value
    assert state.get_memory("key2") is None
    
    # Get a non-existent value with default
    assert state.get_memory("key2", "default") == "default"
    
    # Set another value
    state.set_memory("key2", {"nested": "value"})
    assert state.get_memory("key2")["nested"] == "value"
    
    # Clear memory
    state.clear_memory()
    assert state.memory == {}

def test_state_active_agent():
    """Test setting and getting the active agent."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789"
    )
    
    # Initially None
    assert state.get_active_agent() is None
    
    # Set active agent
    state.set_active_agent("intake_agent")
    assert state.get_active_agent() == "intake_agent"
    
    # Change active agent
    state.set_active_agent("research_agent")
    assert state.get_active_agent() == "research_agent"

def test_state_serialization():
    """Test serializing and deserializing the state."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        matter_id="matter-101112",
        initial_memory={"key": "value"},
        initial_messages=[{"role": "system", "content": "Hello"}]
    )
    
    # Serialize to JSON
    json_str = state.to_json()
    
    # Deserialize from JSON
    new_state = AiLexState.from_json(json_str)
    
    # Verify the deserialized state
    assert new_state.tenant_id == "tenant-123"
    assert new_state.user_id == "user-456"
    assert new_state.thread_id == "thread-789"
    assert new_state.matter_id == "matter-101112"
    assert new_state.memory["key"] == "value"
    assert new_state.messages[0]["role"] == "system"
    assert new_state.messages[0]["content"] == "Hello"

def test_state_snapshot():
    """Test creating a snapshot of the state."""
    state = create_state(
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789"
    )
    
    # Add some data
    state.add_message("user", "Hello")
    state.set_memory("key", "value")
    
    # Create a snapshot
    snapshot = state.snapshot()
    
    # Verify the snapshot
    assert snapshot["tenant_id"] == "tenant-123"
    assert snapshot["user_id"] == "user-456"
    assert snapshot["thread_id"] == "thread-789"
    assert snapshot["memory"]["key"] == "value"
    assert snapshot["messages"][0]["role"] == "user"
    assert snapshot["messages"][0]["content"] == "Hello"
