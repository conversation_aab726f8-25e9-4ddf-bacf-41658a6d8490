"""
Tests for the LangGraph state persistence module.

This module implements a phased testing strategy for the LangGraph state persistence:
1. Contract tests for serialization/deserialization
2. Unit tests for persistence functions
3. Integration tests with the database
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from langchain_core.documents import Document
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from pi_lawyer.state.langgraph_state import (
    BaseAgentState,
    IntakeAgentState,
    ResearchAgentState,
    UserContext,
)
from pi_lawyer.state.persistence import (
    delete_state,
    deserialize_message,
    deserialize_state,
    list_states,
    load_state,
    save_state,
    serialize_message,
    serialize_state,
)

# ============================================================================
# Phase 1: Contract Tests
# ============================================================================

class TestSerializationContracts:
    """Contract tests for serialization/deserialization."""

    def test_message_serialization_contract(self):
        """Test that messages can be serialized and deserialized."""
        # Test HumanMessage
        human_msg = HumanMessage(content="Hello")
        serialized = serialize_message(human_msg)
        deserialized = deserialize_message(serialized)
        
        assert isinstance(deserialized, HumanMessage)
        assert deserialized.content == "Hello"
        
        # Test AIMessage
        ai_msg = AIMessage(content="Hi there")
        serialized = serialize_message(ai_msg)
        deserialized = deserialize_message(serialized)
        
        assert isinstance(deserialized, AIMessage)
        assert deserialized.content == "Hi there"
        
        # Test SystemMessage
        system_msg = SystemMessage(content="System message")
        serialized = serialize_message(system_msg)
        deserialized = deserialize_message(serialized)
        
        assert isinstance(deserialized, SystemMessage)
        assert deserialized.content == "System message"

    def test_state_serialization_contract(self):
        """Test that states can be serialized and deserialized."""
        # Create a test user context
        user_context = UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="attorney"
        )
        
        # Create a test state
        state = BaseAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="intake",
            user_context=user_context,
            messages=[
                HumanMessage(content="Hello"),
                AIMessage(content="Hi there")
            ]
        )
        
        # Serialize the state
        serialized = serialize_state(state)
        
        # Check that the serialized state has the expected fields
        assert "tenant_id" in serialized
        assert "user_id" in serialized
        assert "thread_id" in serialized
        assert "agent_type" in serialized
        assert "user_context" in serialized
        assert "messages" in serialized
        
        # Check that messages are serialized correctly
        assert len(serialized["messages"]) == 2
        assert serialized["messages"][0]["type"] == "HumanMessage"
        assert serialized["messages"][0]["content"] == "Hello"
        assert serialized["messages"][1]["type"] == "AIMessage"
        assert serialized["messages"][1]["content"] == "Hi there"
        
        # Deserialize the state
        deserialized = deserialize_state("intake", serialized)
        
        # Check that the deserialized state has the expected fields
        assert isinstance(deserialized, IntakeAgentState)
        assert deserialized.tenant_id == "tenant-456"
        assert deserialized.user_id == "user-123"
        assert deserialized.thread_id == "thread-789"
        assert deserialized.agent_type == "intake"
        
        # Check that messages are deserialized correctly
        assert len(deserialized.messages) == 2
        assert isinstance(deserialized.messages[0], HumanMessage)
        assert deserialized.messages[0].content == "Hello"
        assert isinstance(deserialized.messages[1], AIMessage)
        assert deserialized.messages[1].content == "Hi there"


# ============================================================================
# Phase 2: Unit Tests
# ============================================================================

class TestMessageSerialization:
    """Unit tests for message serialization/deserialization."""

    def test_serialize_human_message(self):
        """Test serializing a HumanMessage."""
        message = HumanMessage(content="Hello")
        serialized = serialize_message(message)
        
        assert serialized["type"] == "HumanMessage"
        assert serialized["content"] == "Hello"
        assert "additional_kwargs" in serialized

    def test_serialize_ai_message(self):
        """Test serializing an AIMessage."""
        message = AIMessage(content="Hi there")
        serialized = serialize_message(message)
        
        assert serialized["type"] == "AIMessage"
        assert serialized["content"] == "Hi there"
        assert "additional_kwargs" in serialized

    def test_serialize_system_message(self):
        """Test serializing a SystemMessage."""
        message = SystemMessage(content="System message")
        serialized = serialize_message(message)
        
        assert serialized["type"] == "SystemMessage"
        assert serialized["content"] == "System message"
        assert "additional_kwargs" in serialized

    def test_deserialize_human_message(self):
        """Test deserializing a HumanMessage."""
        serialized = {
            "type": "HumanMessage",
            "content": "Hello",
            "additional_kwargs": {}
        }
        deserialized = deserialize_message(serialized)
        
        assert isinstance(deserialized, HumanMessage)
        assert deserialized.content == "Hello"

    def test_deserialize_ai_message(self):
        """Test deserializing an AIMessage."""
        serialized = {
            "type": "AIMessage",
            "content": "Hi there",
            "additional_kwargs": {}
        }
        deserialized = deserialize_message(serialized)
        
        assert isinstance(deserialized, AIMessage)
        assert deserialized.content == "Hi there"

    def test_deserialize_system_message(self):
        """Test deserializing a SystemMessage."""
        serialized = {
            "type": "SystemMessage",
            "content": "System message",
            "additional_kwargs": {}
        }
        deserialized = deserialize_message(serialized)
        
        assert isinstance(deserialized, SystemMessage)
        assert deserialized.content == "System message"


class TestStateSerialization:
    """Unit tests for state serialization/deserialization."""

    @pytest.fixture
    def user_context(self) -> UserContext:
        """Create a test user context."""
        return UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="attorney",
            assigned_case_ids=["case-1", "case-2"],
            settings={"theme": "dark"}
        )

    def test_serialize_base_state(self, user_context: UserContext):
        """Test serializing a BaseAgentState."""
        state = BaseAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="intake",
            user_context=user_context,
            messages=[
                HumanMessage(content="Hello"),
                AIMessage(content="Hi there")
            ]
        )
        
        serialized = serialize_state(state)
        
        assert serialized["tenant_id"] == "tenant-456"
        assert serialized["user_id"] == "user-123"
        assert serialized["thread_id"] == "thread-789"
        assert serialized["agent_type"] == "intake"
        assert "user_context" in serialized
        assert serialized["user_context"]["user_id"] == "user-123"
        assert serialized["user_context"]["tenant_id"] == "tenant-456"
        assert serialized["user_context"]["role"] == "attorney"
        assert len(serialized["messages"]) == 2
        assert serialized["messages"][0]["type"] == "HumanMessage"
        assert serialized["messages"][0]["content"] == "Hello"
        assert serialized["messages"][1]["type"] == "AIMessage"
        assert serialized["messages"][1]["content"] == "Hi there"

    def test_serialize_research_state(self, user_context: UserContext):
        """Test serializing a ResearchAgentState."""
        state = ResearchAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="research",
            user_context=user_context,
            question="What is the statute of limitations for personal injury in Texas?",
            jurisdiction="texas",
            practice_areas={"personal_injury"},
            query_type="public",
            case_id="case-123",
            legal_documents=[
                Document(page_content="Texas has a 2-year statute of limitations for personal injury claims.")
            ]
        )
        
        serialized = serialize_state(state)
        
        assert serialized["tenant_id"] == "tenant-456"
        assert serialized["user_id"] == "user-123"
        assert serialized["thread_id"] == "thread-789"
        assert serialized["agent_type"] == "research"
        assert serialized["question"] == "What is the statute of limitations for personal injury in Texas?"
        assert serialized["jurisdiction"] == "texas"
        assert serialized["practice_areas"] == ["personal_injury"]
        assert serialized["query_type"] == "public"
        assert serialized["case_id"] == "case-123"
        assert len(serialized["legal_documents"]) == 1
        assert "page_content" in serialized["legal_documents"][0]
        assert serialized["legal_documents"][0]["page_content"] == "Texas has a 2-year statute of limitations for personal injury claims."

    def test_deserialize_base_state(self):
        """Test deserializing a BaseAgentState."""
        serialized = {
            "tenant_id": "tenant-456",
            "user_id": "user-123",
            "thread_id": "thread-789",
            "agent_type": "intake",
            "user_context": {
                "user_id": "user-123",
                "tenant_id": "tenant-456",
                "role": "attorney",
                "assigned_case_ids": ["case-1", "case-2"],
                "settings": {"theme": "dark"}
            },
            "messages": [
                {
                    "type": "HumanMessage",
                    "content": "Hello",
                    "additional_kwargs": {}
                },
                {
                    "type": "AIMessage",
                    "content": "Hi there",
                    "additional_kwargs": {}
                }
            ],
            "memory": {},
            "created_at": "2023-01-01T00:00:00+00:00",
            "updated_at": "2023-01-01T00:00:00+00:00"
        }
        
        deserialized = deserialize_state("intake", serialized)
        
        assert isinstance(deserialized, IntakeAgentState)
        assert deserialized.tenant_id == "tenant-456"
        assert deserialized.user_id == "user-123"
        assert deserialized.thread_id == "thread-789"
        assert deserialized.agent_type == "intake"
        assert deserialized.user_context.user_id == "user-123"
        assert deserialized.user_context.tenant_id == "tenant-456"
        assert deserialized.user_context.role == "attorney"
        assert len(deserialized.messages) == 2
        assert isinstance(deserialized.messages[0], HumanMessage)
        assert deserialized.messages[0].content == "Hello"
        assert isinstance(deserialized.messages[1], AIMessage)
        assert deserialized.messages[1].content == "Hi there"


# ============================================================================
# Phase 3: Integration Tests
# ============================================================================

@pytest.mark.asyncio
class TestDatabaseOperations:
    """Integration tests for database operations."""

    @pytest.fixture
    def mock_db_client(self):
        """Create a mock database client."""
        mock_client = AsyncMock()
        mock_table = AsyncMock()
        mock_select = AsyncMock()
        mock_eq = AsyncMock()
        mock_single = AsyncMock()
        mock_insert = AsyncMock()
        mock_update = AsyncMock()
        mock_delete = AsyncMock()
        mock_execute = AsyncMock()
        
        # Configure the mock chain
        mock_client.table.return_value = mock_table
        mock_table.select.return_value = mock_select
        mock_select.eq.return_value = mock_eq
        mock_eq.eq.return_value = mock_eq
        mock_eq.single.return_value = mock_single
        mock_table.insert.return_value = mock_insert
        mock_insert.execute.return_value = mock_execute
        mock_table.update.return_value = mock_update
        mock_update.eq.return_value = mock_eq
        mock_table.delete.return_value = mock_delete
        mock_delete.eq.return_value = mock_eq
        
        return mock_client

    @pytest.fixture
    def user_context(self) -> UserContext:
        """Create a test user context."""
        return UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="attorney",
            assigned_case_ids=["case-1", "case-2"],
            settings={"theme": "dark"}
        )

    @pytest.fixture
    def test_state(self, user_context: UserContext) -> BaseAgentState:
        """Create a test state."""
        return BaseAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="intake",
            user_context=user_context,
            messages=[
                HumanMessage(content="Hello"),
                AIMessage(content="Hi there")
            ]
        )

    @patch("pi_lawyer.state.persistence.get_db_client")
    async def test_save_state_new(self, mock_get_db_client, mock_db_client, test_state):
        """Test saving a new state."""
        # Configure the mock
        mock_get_db_client.return_value = mock_db_client
        mock_db_client.table().select().eq().single.return_value = None
        mock_db_client.table().insert().execute.return_value = MagicMock(data=[{"id": "record-123"}])
        
        # Call the function
        result = await save_state(test_state)
        
        # Check the result
        assert result == "record-123"
        
        # Verify the mock calls
        mock_get_db_client.assert_called_once()
        mock_db_client.table.assert_called_with("agent_states")
        mock_db_client.table().select.assert_called_with("id")
        mock_db_client.table().select().eq.assert_called_with("thread_id", "thread-789")
        mock_db_client.table().select().eq().single.assert_called_once()
        mock_db_client.table().insert.assert_called_once()
        mock_db_client.table().insert().execute.assert_called_once()

    @patch("pi_lawyer.state.persistence.get_db_client")
    async def test_save_state_existing(self, mock_get_db_client, mock_db_client, test_state):
        """Test updating an existing state."""
        # Configure the mock
        mock_get_db_client.return_value = mock_db_client
        mock_db_client.table().select().eq().single.return_value = {"id": "record-123"}
        
        # Call the function
        result = await save_state(test_state)
        
        # Check the result
        assert result == "record-123"
        
        # Verify the mock calls
        mock_get_db_client.assert_called_once()
        mock_db_client.table.assert_called_with("agent_states")
        mock_db_client.table().select.assert_called_with("id")
        mock_db_client.table().select().eq.assert_called_with("thread_id", "thread-789")
        mock_db_client.table().select().eq().single.assert_called_once()
        mock_db_client.table().update.assert_called_once()
        mock_db_client.table().update().eq.assert_called_with("id", "record-123")

    @patch("pi_lawyer.state.persistence.get_db_client")
    async def test_load_state(self, mock_get_db_client, mock_db_client):
        """Test loading a state."""
        # Configure the mock
        mock_get_db_client.return_value = mock_db_client
        mock_db_client.table().select().eq().eq().single.return_value = {
            "id": "record-123",
            "thread_id": "thread-789",
            "tenant_id": "tenant-456",
            "user_id": "user-123",
            "agent_type": "intake",
            "state_data": {
                "tenant_id": "tenant-456",
                "user_id": "user-123",
                "thread_id": "thread-789",
                "agent_type": "intake",
                "user_context": {
                    "user_id": "user-123",
                    "tenant_id": "tenant-456",
                    "role": "attorney",
                    "assigned_case_ids": [],
                    "settings": {}
                },
                "messages": [],
                "memory": {},
                "created_at": "2023-01-01T00:00:00+00:00",
                "updated_at": "2023-01-01T00:00:00+00:00"
            },
            "created_at": "2023-01-01T00:00:00+00:00",
            "updated_at": "2023-01-01T00:00:00+00:00"
        }
        
        # Call the function
        result = await load_state("thread-789", "tenant-456")
        
        # Check the result
        assert result is not None
        assert isinstance(result, IntakeAgentState)
        assert result.tenant_id == "tenant-456"
        assert result.user_id == "user-123"
        assert result.thread_id == "thread-789"
        assert result.agent_type == "intake"
        
        # Verify the mock calls
        mock_get_db_client.assert_called_once()
        mock_db_client.table.assert_called_with("agent_states")
        mock_db_client.table().select.assert_called_with("*")
        mock_db_client.table().select().eq.assert_called_with("thread_id", "thread-789")
        mock_db_client.table().select().eq().eq.assert_called_with("tenant_id", "tenant-456")
        mock_db_client.table().select().eq().eq().single.assert_called_once()

    @patch("pi_lawyer.state.persistence.get_db_client")
    async def test_delete_state(self, mock_get_db_client, mock_db_client):
        """Test deleting a state."""
        # Configure the mock
        mock_get_db_client.return_value = mock_db_client
        mock_db_client.table().delete().eq().eq().execute.return_value = MagicMock(data=[{"id": "record-123"}])
        
        # Call the function
        result = await delete_state("thread-789", "tenant-456")
        
        # Check the result
        assert result is True
        
        # Verify the mock calls
        mock_get_db_client.assert_called_once()
        mock_db_client.table.assert_called_with("agent_states")
        mock_db_client.table().delete.assert_called_once()
        mock_db_client.table().delete().eq.assert_called_with("thread_id", "thread-789")
        mock_db_client.table().delete().eq().eq.assert_called_with("tenant_id", "tenant-456")
        mock_db_client.table().delete().eq().eq().execute.assert_called_once()

    @patch("pi_lawyer.state.persistence.get_db_client")
    async def test_list_states(self, mock_get_db_client, mock_db_client):
        """Test listing states."""
        # Configure the mock
        mock_get_db_client.return_value = mock_db_client
        mock_db_client.table().select().eq().execute.return_value = MagicMock(data=[
            {
                "thread_id": "thread-789",
                "agent_type": "intake",
                "user_id": "user-123",
                "created_at": "2023-01-01T00:00:00+00:00",
                "updated_at": "2023-01-01T00:00:00+00:00"
            },
            {
                "thread_id": "thread-790",
                "agent_type": "research",
                "user_id": "user-123",
                "created_at": "2023-01-02T00:00:00+00:00",
                "updated_at": "2023-01-02T00:00:00+00:00"
            }
        ])
        
        # Call the function
        result = await list_states("tenant-456")
        
        # Check the result
        assert len(result) == 2
        assert result[0]["thread_id"] == "thread-789"
        assert result[0]["agent_type"] == "intake"
        assert result[1]["thread_id"] == "thread-790"
        assert result[1]["agent_type"] == "research"
        
        # Verify the mock calls
        mock_get_db_client.assert_called_once()
        mock_db_client.table.assert_called_with("agent_states")
        mock_db_client.table().select.assert_called_with("thread_id, agent_type, user_id, created_at, updated_at")
        mock_db_client.table().select().eq.assert_called_with("tenant_id", "tenant-456")
        mock_db_client.table().select().eq().execute.assert_called_once()
