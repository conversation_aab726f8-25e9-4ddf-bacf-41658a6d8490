"""
Tests for the LangGraph state management utilities.

This module implements a phased testing strategy for the LangGraph state management:
1. Contract tests for state creation and conversion
2. Unit tests for state management utilities
3. Integration tests with LangGraph
"""

from unittest.mock import patch

import pytest

from pi_lawyer.state.langgraph_state import (
    STATE_VERSION,
    BaseLangGraphState,
    DeadlineLangGraphState,
    DocumentLangGraphState,
    EventLangGraphState,
    IntakeAgentState,
    IntakeLangGraphState,
    ResearchAgentState,
    ResearchLangGraphState,
    StateManager,
    UserContext,
    create_state,
    create_typed_state,
    get_state_class_for_agent_type,
)


# Contract tests for state creation and conversion
class TestStateCreationAndConversion:
    """Contract tests for state creation and conversion."""

    def test_create_state_with_minimal_params(self):
        """Test creating a state with minimal parameters."""
        # Arrange
        agent_type = "research"
        tenant_id = "tenant-123"
        user_id = "user-456"
        thread_id = "thread-789"
        
        # Act
        state = create_state(
            agent_type=agent_type,
            tenant_id=tenant_id,
            user_id=user_id,
            thread_id=thread_id
        )
        
        # Assert
        assert isinstance(state, ResearchAgentState)
        assert state.agent_type == agent_type
        assert state.tenant_id == tenant_id
        assert state.user_id == user_id
        assert state.thread_id == thread_id
        assert state.version == STATE_VERSION
        assert isinstance(state.user_context, UserContext)
        assert state.user_context.tenant_id == tenant_id
        assert state.user_context.user_id == user_id
        assert state.user_context.role == "staff"  # Default role
    
    def test_create_state_with_user_context(self):
        """Test creating a state with a user context."""
        # Arrange
        agent_type = "intake"
        tenant_id = "tenant-123"
        user_id = "user-456"
        thread_id = "thread-789"
        user_context = UserContext(
            user_id=user_id,
            tenant_id=tenant_id,
            role="attorney",
            assigned_case_ids=["case-123", "case-456"],
            settings={"theme": "dark"}
        )
        
        # Act
        state = create_state(
            agent_type=agent_type,
            tenant_id=tenant_id,
            user_id=user_id,
            thread_id=thread_id,
            user_context=user_context
        )
        
        # Assert
        assert isinstance(state, IntakeAgentState)
        assert state.user_context == user_context
        assert state.user_context.role == "attorney"
        assert state.user_context.assigned_case_ids == ["case-123", "case-456"]
        assert state.user_context.settings == {"theme": "dark"}
    
    def test_create_state_with_agent_specific_params(self):
        """Test creating a state with agent-specific parameters."""
        # Arrange
        agent_type = "research"
        tenant_id = "tenant-123"
        user_id = "user-456"
        thread_id = "thread-789"
        question = "What is the statute of limitations for personal injury in Texas?"
        jurisdiction = "texas"
        
        # Act
        state = create_state(
            agent_type=agent_type,
            tenant_id=tenant_id,
            user_id=user_id,
            thread_id=thread_id,
            question=question,
            jurisdiction=jurisdiction
        )
        
        # Assert
        assert isinstance(state, ResearchAgentState)
        assert state.question == question
        assert state.jurisdiction == jurisdiction
    
    def test_create_typed_state(self):
        """Test creating a typed state dictionary from a state object."""
        # Arrange
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789",
            question="What is the statute of limitations for personal injury in Texas?"
        )
        
        # Act
        typed_state = create_typed_state(state)
        
        # Assert
        assert isinstance(typed_state, dict)
        assert typed_state["agent_type"] == "research"
        assert typed_state["tenant_id"] == "tenant-123"
        assert typed_state["user_id"] == "user-456"
        assert typed_state["thread_id"] == "thread-789"
        assert typed_state["question"] == "What is the statute of limitations for personal injury in Texas?"
        assert isinstance(typed_state["user_context"], dict)
        assert typed_state["version"] == STATE_VERSION
    
    def test_get_state_class_for_agent_type(self):
        """Test getting the appropriate LangGraph state class for an agent type."""
        # Act & Assert
        assert get_state_class_for_agent_type("intake") == IntakeLangGraphState
        assert get_state_class_for_agent_type("research") == ResearchLangGraphState
        assert get_state_class_for_agent_type("document") == DocumentLangGraphState
        assert get_state_class_for_agent_type("deadline") == DeadlineLangGraphState
        assert get_state_class_for_agent_type("event") == EventLangGraphState
        assert get_state_class_for_agent_type("supervisor") == BaseLangGraphState
        assert get_state_class_for_agent_type("echo") == BaseLangGraphState


# Unit tests for state management utilities
@pytest.mark.asyncio
class TestStateManager:
    """Unit tests for the StateManager class."""
    
    async def test_create_state(self):
        """Test creating a state using StateManager."""
        # Arrange
        agent_type = "research"
        tenant_id = "tenant-123"
        user_id = "user-456"
        thread_id = "thread-789"
        
        # Act
        state = StateManager.create_state(
            agent_type=agent_type,
            tenant_id=tenant_id,
            user_id=user_id,
            thread_id=thread_id
        )
        
        # Assert
        assert isinstance(state, ResearchAgentState)
        assert state.agent_type == agent_type
        assert state.tenant_id == tenant_id
        assert state.user_id == user_id
        assert state.thread_id == thread_id
    
    def test_to_typed_dict(self):
        """Test converting a state to a typed dictionary."""
        # Arrange
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789",
            question="What is the statute of limitations for personal injury in Texas?"
        )
        
        # Act
        typed_dict = StateManager.to_typed_dict(state)
        
        # Assert
        assert isinstance(typed_dict, dict)
        assert typed_dict["agent_type"] == "research"
        assert typed_dict["question"] == "What is the statute of limitations for personal injury in Texas?"
    
    def test_from_typed_dict(self):
        """Test creating a state from a typed dictionary."""
        # Arrange
        typed_dict = {
            "agent_type": "research",
            "tenant_id": "tenant-123",
            "user_id": "user-456",
            "thread_id": "thread-789",
            "question": "What is the statute of limitations for personal injury in Texas?",
            "user_context": {
                "user_id": "user-456",
                "tenant_id": "tenant-123",
                "role": "attorney",
                "assigned_case_ids": ["case-123"],
                "settings": {}
            }
        }
        
        # Act
        state = StateManager.from_typed_dict(typed_dict)
        
        # Assert
        assert isinstance(state, ResearchAgentState)
        assert state.agent_type == "research"
        assert state.tenant_id == "tenant-123"
        assert state.user_id == "user-456"
        assert state.thread_id == "thread-789"
        assert state.question == "What is the statute of limitations for personal injury in Texas?"
        assert state.user_context.role == "attorney"
    
    def test_migrate_state(self):
        """Test migrating a state to a new version."""
        # Arrange
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789"
        )
        state.version = "0.9.0"  # Set an old version
        
        # Act
        migrated_state = StateManager.migrate_state(state, "1.0.0")
        
        # Assert
        assert migrated_state.version == "1.0.0"
        assert migrated_state is not state  # Should be a clone
    
    @patch("pi_lawyer.state.persistence.save_state")
    async def test_save_state(self, mock_save_state):
        """Test saving a state to the database."""
        # Arrange
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789"
        )
        mock_save_state.return_value = "record-123"
        
        # Act
        result = await StateManager.save_state(state)
        
        # Assert
        assert result == "record-123"
        mock_save_state.assert_called_once_with(state)
    
    @patch("pi_lawyer.state.persistence.load_state")
    async def test_load_state(self, mock_load_state):
        """Test loading a state from the database."""
        # Arrange
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789"
        )
        mock_load_state.return_value = state
        
        # Act
        result = await StateManager.load_state("thread-789", "tenant-123")
        
        # Assert
        assert result == state
        mock_load_state.assert_called_once_with("thread-789", "tenant-123")
