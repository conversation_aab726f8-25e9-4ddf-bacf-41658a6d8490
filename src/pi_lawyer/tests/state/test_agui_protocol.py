"""
Tests for AG-UI protocol compliance in LangGraph state management.

This module tests that our implementation is emitting STATE_DELTA events
with minimal JSON-patches and not sending full state snapshots after mutation operations.
"""

import json

import jsonpatch
from langchain_core.messages import AIMessage, HumanMessage

from src.pi_lawyer.state.langgraph_state import create_state, create_typed_state


# Custom JSON encoder to handle non-serializable types
def json_serializer(obj):
    if isinstance(obj, set):
        return list(obj)
    elif hasattr(obj, 'model_dump') and callable(obj.model_dump):
        return obj.model_dump()
    elif hasattr(obj, 'dict') and callable(obj.dict):
        # Fallback for older Pydantic versions
        return obj.dict()
    elif hasattr(obj, '__dict__'):
        return obj.__dict__
    raise TypeError(f"Type {type(obj)} not serializable")


def test_state_delta_minimal_patches():
    """Test that state deltas are minimal JSON patches."""
    # Create an initial state
    state = create_state(
        agent_type="research",
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        question="What is the statute of limitations for personal injury in Texas?"
    )

    # Convert to TypedDict for LangGraph
    initial_state_dict = create_typed_state(state)

    # Use the global json_serializer function
    initial_state_json = json.dumps(initial_state_dict, default=json_serializer)

    # Make a change to the state
    state.add_message(HumanMessage(content="Can you provide more details?"))

    # Convert to TypedDict again
    updated_state_dict = create_typed_state(state)
    updated_state_json = json.dumps(updated_state_dict, default=json_serializer)

    # Generate a JSON patch
    patch = jsonpatch.make_patch(json.loads(initial_state_json), json.loads(updated_state_json))
    patch_ops = patch.patch

    # Print the patch for inspection
    print("\nJSON Patch for adding a message:")
    print(json.dumps(patch_ops, indent=2))

    # Verify that the patch only contains the necessary changes
    assert len(patch_ops) < 3, f"Patch has {len(patch_ops)} operations, expected fewer than 3"

    # Verify that the patch doesn't contain the entire state
    patch_size = len(json.dumps(patch_ops))
    state_size = len(updated_state_json)

    print(f"Patch size: {patch_size} bytes")
    print(f"Full state size: {state_size} bytes")

    assert patch_size < state_size / 2, f"Patch size ({patch_size}) is not significantly smaller than full state size ({state_size})"

    # Apply the patch to verify it works correctly
    patched_state = jsonpatch.apply_patch(json.loads(initial_state_json), patch_ops)

    # Verify that the patched state matches the updated state
    assert patched_state == json.loads(updated_state_json)


def test_multiple_state_changes():
    """Test that multiple state changes result in minimal patches."""
    # Create an initial state
    state = create_state(
        agent_type="research",
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        question="What is the statute of limitations for personal injury in Texas?"
    )

    # Convert to TypedDict for LangGraph
    initial_state_dict = create_typed_state(state)

    # Use the global json_serializer function
    initial_state_json = json.dumps(initial_state_dict, default=json_serializer)

    # Make multiple changes to the state
    state.add_message(HumanMessage(content="Can you provide more details?"))
    state.add_message(AIMessage(content="The statute of limitations for personal injury in Texas is generally 2 years."))
    state.set_memory("search_query", "Texas personal injury statute of limitations")
    state.set_memory("search_results", ["Texas Civil Practice and Remedies Code § 16.003"])

    # Convert to TypedDict again
    updated_state_dict = create_typed_state(state)
    updated_state_json = json.dumps(updated_state_dict, default=json_serializer)

    # Generate a JSON patch
    patch = jsonpatch.make_patch(json.loads(initial_state_json), json.loads(updated_state_json))
    patch_ops = patch.patch

    # Print the patch for inspection
    print("\nJSON Patch for multiple changes:")
    print(json.dumps(patch_ops, indent=2))

    # Verify that the patch only contains the necessary changes
    assert len(patch_ops) < 10, f"Patch has {len(patch_ops)} operations, expected fewer than 10"

    # Verify that the patch doesn't contain the entire state
    patch_size = len(json.dumps(patch_ops))
    state_size = len(updated_state_json)

    print(f"Patch size: {patch_size} bytes")
    print(f"Full state size: {state_size} bytes")

    # For multiple changes, the patch might be larger, but still smaller than the full state
    assert patch_size < state_size, f"Patch size ({patch_size}) is not smaller than full state size ({state_size})"

    # Apply the patch to verify it works correctly
    patched_state = jsonpatch.apply_patch(json.loads(initial_state_json), patch_ops)

    # Verify that the patched state matches the updated state
    assert patched_state == json.loads(updated_state_json)


def test_reducer_function_changes():
    """Test that changes made through reducer functions result in minimal patches."""
    # Create an initial state
    state = create_state(
        agent_type="research",
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        question="What is the statute of limitations for personal injury in Texas?"
    )

    # Convert to TypedDict for LangGraph
    initial_state_dict = create_typed_state(state)

    # We'll use the global json_serializer function

    # Simulate a LangGraph node that returns a delta
    delta = {
        "messages": [AIMessage(content="The statute of limitations for personal injury in Texas is generally 2 years.")],
        "memory": {"search_query": "Texas personal injury statute of limitations"}
    }

    # Apply the delta to the state dictionary using our reducer functions
    from src.pi_lawyer.state.langgraph_state import add_messages, merge_dict

    updated_state_dict = initial_state_dict.copy()
    updated_state_dict["messages"] = add_messages(initial_state_dict["messages"], delta["messages"])
    updated_state_dict["memory"] = merge_dict(initial_state_dict["memory"], delta["memory"])

    # Convert to JSON strings first to handle non-serializable types
    initial_json = json.dumps(initial_state_dict, default=json_serializer)
    updated_json = json.dumps(updated_state_dict, default=json_serializer)

    # Generate a JSON patch
    patch = jsonpatch.make_patch(json.loads(initial_json), json.loads(updated_json))
    patch_ops = patch.patch

    # Print the patch for inspection
    print("\nJSON Patch for reducer function changes:")
    print(json.dumps(patch_ops, indent=2, default=str))

    # Verify that the patch only contains the necessary changes
    assert len(patch_ops) < 5, f"Patch has {len(patch_ops)} operations, expected fewer than 5"

    # Verify that the patch doesn't contain the entire state
    patch_size = len(json.dumps(patch_ops, default=str))
    state_size = len(json.dumps(updated_state_dict, default=str))

    print(f"Patch size: {patch_size} bytes")
    print(f"Full state size: {state_size} bytes")

    assert patch_size < state_size / 2, f"Patch size ({patch_size}) is not significantly smaller than full state size ({state_size})"


if __name__ == "__main__":
    # Run the tests
    test_state_delta_minimal_patches()
    test_multiple_state_changes()
    test_reducer_function_changes()
