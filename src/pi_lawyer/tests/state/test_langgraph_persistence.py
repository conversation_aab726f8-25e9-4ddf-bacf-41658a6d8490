"""
Tests for the LangGraph state persistence layer.

This module implements a comprehensive testing strategy for the LangGraph state persistence:
1. Unit tests for serialization/deserialization
2. Integration tests for state persistence with LangGraph
3. Tenant isolation tests to verify security
4. Performance tests to ensure operations meet response time requirements
"""

from unittest.mock import MagicMock, patch

import pytest
from langchain_core.documents import Document
from langchain_core.messages import (
    AIMessage,
    FunctionMessage,
    HumanMessage,
    SystemMessage,
)

from pi_lawyer.state.langgraph_state import (
    BaseAgentState,
    ResearchAgentState,
    UserContext,
)
from pi_lawyer.state.persistence import (
    STATE_SCHEMA,
    STATE_TABLE,
    delete_state,
    deserialize_message,
    deserialize_state,
    load_state,
    save_state,
    serialize_message,
    serialize_state,
)


# Unit tests for serialization/deserialization
class TestSerializationDeserialization:
    """Unit tests for serialization and deserialization functions."""

    def test_serialize_deserialize_message(self):
        """Test serialization and deserialization of messages."""
        # Test human message
        human_msg = HumanMessage(content="Hello, world!")
        serialized = serialize_message(human_msg)
        deserialized = deserialize_message(serialized)
        assert isinstance(deserialized, HumanMessage)
        assert deserialized.content == "Hello, world!"

        # Test AI message
        ai_msg = AIMessage(content="I'm an AI assistant.")
        serialized = serialize_message(ai_msg)
        deserialized = deserialize_message(serialized)
        assert isinstance(deserialized, AIMessage)
        assert deserialized.content == "I'm an AI assistant."

        # Test system message
        sys_msg = SystemMessage(content="System instruction")
        serialized = serialize_message(sys_msg)
        deserialized = deserialize_message(serialized)
        assert isinstance(deserialized, SystemMessage)
        assert deserialized.content == "System instruction"

        # Test function message with additional_kwargs
        func_msg = FunctionMessage(
            content="Function result",
            name="test_function",
            additional_kwargs={"result": {"value": 42}}
        )
        serialized = serialize_message(func_msg)
        deserialized = deserialize_message(serialized)
        assert isinstance(deserialized, FunctionMessage)
        assert deserialized.content == "Function result"
        assert deserialized.name == "test_function"
        assert deserialized.additional_kwargs["result"]["value"] == 42

    def test_serialize_deserialize_base_state(self):
        """Test serialization and deserialization of base state."""
        # Create a base state
        state = BaseAgentState(
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789",
            agent_type="research",
            user_context=UserContext(
                id="user-456",
                email="<EMAIL>",
                role="attorney",
                tenant_id="tenant-123",
                permissions=["read", "write"]
            ),
            messages=[
                HumanMessage(content="What is the statute of limitations for personal injury in Texas?"),
                AIMessage(content="In Texas, the statute of limitations for personal injury claims is 2 years from the date of the injury.")
            ],
            memory={"last_query": "statute of limitations"}
        )

        # Serialize and deserialize
        serialized = serialize_state(state)
        deserialized = deserialize_state("research", serialized)

        # Verify
        assert isinstance(deserialized, BaseAgentState)
        assert deserialized.tenant_id == "tenant-123"
        assert deserialized.user_id == "user-456"
        assert deserialized.thread_id == "thread-789"
        assert deserialized.agent_type == "research"
        assert deserialized.user_context.id == "user-456"
        assert deserialized.user_context.email == "<EMAIL>"
        assert len(deserialized.messages) == 2
        assert isinstance(deserialized.messages[0], HumanMessage)
        assert isinstance(deserialized.messages[1], AIMessage)
        assert deserialized.memory["last_query"] == "statute of limitations"

    def test_serialize_deserialize_research_state(self):
        """Test serialization and deserialization of research state."""
        # Create a research state
        state = ResearchAgentState(
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789",
            agent_type="research",
            user_context=UserContext(
                id="user-456",
                email="<EMAIL>",
                role="attorney",
                tenant_id="tenant-123",
                permissions=["read", "write"]
            ),
            messages=[
                HumanMessage(content="What is the statute of limitations for personal injury in Texas?"),
                AIMessage(content="In Texas, the statute of limitations for personal injury claims is 2 years from the date of the injury.")
            ],
            memory={"last_query": "statute of limitations"},
            question="What is the statute of limitations for personal injury in Texas?",
            jurisdiction="texas",
            practice_areas={"personal_injury"},
            queries=["texas personal injury statute of limitations"],
            query_type="public",
            legal_documents=[
                Document(page_content="The statute of limitations for personal injury in Texas is 2 years.", metadata={"source": "Texas Civil Practice and Remedies Code"})
            ]
        )

        # Serialize and deserialize
        serialized = serialize_state(state)
        deserialized = deserialize_state("research", serialized)

        # Verify
        assert isinstance(deserialized, ResearchAgentState)
        assert deserialized.tenant_id == "tenant-123"
        assert deserialized.question == "What is the statute of limitations for personal injury in Texas?"
        assert deserialized.jurisdiction == "texas"
        assert "personal_injury" in deserialized.practice_areas
        assert len(deserialized.queries) == 1
        assert deserialized.queries[0] == "texas personal injury statute of limitations"
        assert deserialized.query_type == "public"
        assert len(deserialized.legal_documents) == 1
        assert deserialized.legal_documents[0].page_content == "The statute of limitations for personal injury in Texas is 2 years."
        assert deserialized.legal_documents[0].metadata["source"] == "Texas Civil Practice and Remedies Code"


# Integration tests with database
@pytest.mark.asyncio
class TestDatabaseIntegration:
    """Integration tests for database operations."""

    @pytest.fixture
    async def mock_db_client(self):
        """Mock database client for testing."""
        mock_client = MagicMock()
        mock_client.schema.return_value.table.return_value.select.return_value.eq.return_value.eq.return_value.single.return_value = None
        mock_client.schema.return_value.table.return_value.insert.return_value.execute.return_value.data = [{"id": "record-123"}]
        
        with patch("pi_lawyer.state.persistence.get_db_client", return_value=mock_client):
            yield mock_client

    async def test_save_state(self, mock_db_client):
        """Test saving state to the database."""
        # Create a state
        state = BaseAgentState(
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789",
            agent_type="research",
            user_context=UserContext(
                id="user-456",
                email="<EMAIL>",
                role="attorney",
                tenant_id="tenant-123",
                permissions=["read", "write"]
            ),
            messages=[
                HumanMessage(content="What is the statute of limitations for personal injury in Texas?")
            ]
        )

        # Save the state
        record_id = await save_state(state)

        # Verify
        assert record_id == "record-123"
        mock_db_client.schema.assert_called_with(STATE_SCHEMA)
        mock_db_client.schema.return_value.table.assert_called_with(STATE_TABLE)
        mock_db_client.schema.return_value.table.return_value.insert.assert_called_once()

    async def test_load_state(self, mock_db_client):
        """Test loading state from the database."""
        # Mock the database response
        mock_db_client.schema.return_value.table.return_value.select.return_value.eq.return_value.eq.return_value.single.return_value = {
            "id": "record-123",
            "thread_id": "thread-789",
            "tenant_id": "tenant-123",
            "user_id": "user-456",
            "agent_type": "research",
            "state_data": {
                "tenant_id": "tenant-123",
                "user_id": "user-456",
                "thread_id": "thread-789",
                "agent_type": "research",
                "user_context": {
                    "id": "user-456",
                    "email": "<EMAIL>",
                    "role": "attorney",
                    "tenant_id": "tenant-123",
                    "permissions": ["read", "write"]
                },
                "messages": [
                    {
                        "type": "human",
                        "content": "What is the statute of limitations for personal injury in Texas?"
                    }
                ],
                "memory": {},
                "created_at": "2023-01-01T00:00:00+00:00",
                "updated_at": "2023-01-01T00:00:00+00:00",
                "version": "1.0.0"
            },
            "created_at": "2023-01-01T00:00:00+00:00",
            "updated_at": "2023-01-01T00:00:00+00:00"
        }

        # Load the state
        state = await load_state("thread-789", "tenant-123")

        # Verify
        assert state is not None
        assert state.tenant_id == "tenant-123"
        assert state.user_id == "user-456"
        assert state.thread_id == "thread-789"
        assert state.agent_type == "research"
        assert len(state.messages) == 1
        assert isinstance(state.messages[0], HumanMessage)
        assert state.messages[0].content == "What is the statute of limitations for personal injury in Texas?"

    async def test_delete_state(self, mock_db_client):
        """Test deleting state from the database."""
        # Mock the database response
        mock_db_client.schema.return_value.table.return_value.delete.return_value.eq.return_value.eq.return_value.execute.return_value.data = [{"id": "record-123"}]

        # Delete the state
        result = await delete_state("thread-789", "tenant-123")

        # Verify
        assert result is True
        mock_db_client.schema.assert_called_with(STATE_SCHEMA)
        mock_db_client.schema.return_value.table.assert_called_with(STATE_TABLE)
        mock_db_client.schema.return_value.table.return_value.delete.assert_called_once()
