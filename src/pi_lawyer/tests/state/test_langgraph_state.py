"""
Tests for the LangGraph state schema.

This module implements a phased testing strategy for the LangGraph state schema:
1. Contract tests to verify the schema design
2. Unit tests for all state schema components
3. Integration tests for compatibility with existing systems
"""


import pytest
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from pi_lawyer.state.langgraph_state import (
    BaseAgentState,
    BaseLangGraphState,
    CaseInfo,
    ClientInfo,
    DeadlineAgentState,
    DocumentAgentState,
    IntakeAgentState,
    ResearchAgentState,
    UserContext,
    add_messages,
    create_state,
)

# ============================================================================
# Phase 1: Contract Tests
# ============================================================================

class TestStateContracts:
    """Contract tests for the state schema."""

    def test_base_state_contract(self):
        """Test that BaseLangGraphState follows the required contract for LangGraph."""
        # Verify that BaseLangGraphState has the required fields for LangGraph
        assert "messages" in BaseLangGraphState.__annotations__
        assert "tenant_id" in BaseLangGraphState.__annotations__
        assert "user_id" in BaseLangGraphState.__annotations__
        assert "thread_id" in BaseLangGraphState.__annotations__
        
        # Verify that the messages field has the add_messages reducer
        assert BaseLangGraphState.__annotations__["messages"].__metadata__[0] == add_messages

    def test_message_reducer_contract(self):
        """Test that the message reducer follows the required contract."""
        # Test with a single message
        existing = [HumanMessage(content="Hello")]
        new = AIMessage(content="Hi there")
        result = add_messages(existing, new)
        assert len(result) == 2
        assert result[0].content == "Hello"
        assert result[1].content == "Hi there"
        
        # Test with a list of messages
        existing = [HumanMessage(content="Hello")]
        new = [AIMessage(content="Hi there"), SystemMessage(content="System message")]
        result = add_messages(existing, new)
        assert len(result) == 3
        assert result[0].content == "Hello"
        assert result[1].content == "Hi there"
        assert result[2].content == "System message"

    def test_agent_state_serialization_contract(self):
        """Test that agent states can be serialized and deserialized."""
        # Create a test state
        user_context = UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="attorney"
        )
        
        state = BaseAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="intake",
            user_context=user_context,
            messages=[
                HumanMessage(content="Hello"),
                AIMessage(content="Hi there")
            ]
        )
        
        # Test serialization to dict
        state_dict = state.to_dict()
        assert isinstance(state_dict, dict)
        assert state_dict["tenant_id"] == "tenant-456"
        assert state_dict["user_id"] == "user-123"
        assert state_dict["thread_id"] == "thread-789"
        assert state_dict["agent_type"] == "intake"
        assert len(state_dict["messages"]) == 2
        
        # Test serialization to JSON
        json_str = state.to_json()
        assert isinstance(json_str, str)
        
        # Test deserialization from JSON
        # Note: This will fail because BaseMessage objects can't be directly serialized
        # This is a known limitation and would require a custom serializer
        # We'll test a simplified version
        simple_state = BaseAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="intake",
            user_context=user_context
        )
        json_str = simple_state.to_json()
        loaded_state = BaseAgentState.from_json(json_str)
        assert loaded_state.tenant_id == "tenant-456"
        assert loaded_state.user_id == "user-123"
        assert loaded_state.thread_id == "thread-789"
        assert loaded_state.agent_type == "intake"


# ============================================================================
# Phase 2: Unit Tests
# ============================================================================

class TestBaseAgentState:
    """Unit tests for the BaseAgentState class."""

    @pytest.fixture
    def user_context(self) -> UserContext:
        """Create a test user context."""
        return UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="attorney",
            assigned_case_ids=["case-1", "case-2"],
            settings={"theme": "dark"}
        )

    @pytest.fixture
    def base_state(self, user_context: UserContext) -> BaseAgentState:
        """Create a test base state."""
        return BaseAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="intake",
            user_context=user_context
        )

    def test_initialization(self, base_state: BaseAgentState, user_context: UserContext):
        """Test initialization of BaseAgentState."""
        assert base_state.tenant_id == "tenant-456"
        assert base_state.user_id == "user-123"
        assert base_state.thread_id == "thread-789"
        assert base_state.agent_type == "intake"
        assert base_state.user_context == user_context
        assert isinstance(base_state.messages, list)
        assert len(base_state.messages) == 0
        assert isinstance(base_state.memory, dict)
        assert len(base_state.memory) == 0
        assert isinstance(base_state.created_at, str)
        assert isinstance(base_state.updated_at, str)

    def test_add_message(self, base_state: BaseAgentState):
        """Test adding a message."""
        message = HumanMessage(content="Hello")
        base_state.add_message(message)
        assert len(base_state.messages) == 1
        assert base_state.messages[0].content == "Hello"
        
        # Add another message
        message2 = AIMessage(content="Hi there")
        base_state.add_message(message2)
        assert len(base_state.messages) == 2
        assert base_state.messages[1].content == "Hi there"

    def test_get_last_message(self, base_state: BaseAgentState):
        """Test getting the last message."""
        # No messages
        assert base_state.get_last_message() is None
        
        # Add messages
        message1 = HumanMessage(content="Hello")
        message2 = AIMessage(content="Hi there")
        base_state.add_message(message1)
        base_state.add_message(message2)
        
        # Get last message
        last_message = base_state.get_last_message()
        assert last_message is not None
        assert last_message.content == "Hi there"

    def test_get_messages_by_type(self, base_state: BaseAgentState):
        """Test getting messages by type."""
        # Add messages of different types
        message1 = HumanMessage(content="Hello")
        message2 = AIMessage(content="Hi there")
        message3 = SystemMessage(content="System message")
        message4 = HumanMessage(content="Another human message")
        
        base_state.add_message(message1)
        base_state.add_message(message2)
        base_state.add_message(message3)
        base_state.add_message(message4)
        
        # Get messages by type
        human_messages = base_state.get_messages_by_type(HumanMessage)
        ai_messages = base_state.get_messages_by_type(AIMessage)
        system_messages = base_state.get_messages_by_type(SystemMessage)
        
        assert len(human_messages) == 2
        assert len(ai_messages) == 1
        assert len(system_messages) == 1
        
        assert human_messages[0].content == "Hello"
        assert human_messages[1].content == "Another human message"
        assert ai_messages[0].content == "Hi there"
        assert system_messages[0].content == "System message"

    def test_memory_operations(self, base_state: BaseAgentState):
        """Test memory operations."""
        # Set memory values
        base_state.set_memory("key1", "value1")
        base_state.set_memory("key2", {"nested": "value"})
        
        # Get memory values
        assert base_state.get_memory("key1") == "value1"
        assert base_state.get_memory("key2") == {"nested": "value"}
        assert base_state.get_memory("key3") is None
        assert base_state.get_memory("key3", "default") == "default"


class TestIntakeAgentState:
    """Unit tests for the IntakeAgentState class."""

    @pytest.fixture
    def user_context(self) -> UserContext:
        """Create a test user context."""
        return UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="attorney",
            assigned_case_ids=["case-1", "case-2"],
            settings={"theme": "dark"}
        )

    @pytest.fixture
    def client_info(self) -> ClientInfo:
        """Create test client info."""
        return ClientInfo(
            name="John Doe",
            contact="<EMAIL>",
            injury_description="Car accident injury",
            incident_date="2023-01-15",
            opposing_parties=["ABC Insurance"],
            medical_providers=["City Hospital"]
        )

    @pytest.fixture
    def case_info(self) -> CaseInfo:
        """Create test case info."""
        return CaseInfo(
            case_id="case-123",
            client_id="client-456",
            case_type="Personal Injury",
            status="Active",
            deadlines=[{"description": "File complaint", "date": "2023-03-15"}],
            documents=[{"id": "doc-1", "name": "Police Report"}]
        )

    @pytest.fixture
    def intake_state(self, user_context: UserContext) -> IntakeAgentState:
        """Create a test intake state."""
        return IntakeAgentState(
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            agent_type="intake",
            user_context=user_context
        )

    def test_initialization(self, intake_state: IntakeAgentState):
        """Test initialization of IntakeAgentState."""
        assert intake_state.tenant_id == "tenant-456"
        assert intake_state.user_id == "user-123"
        assert intake_state.thread_id == "thread-789"
        assert intake_state.agent_type == "intake"
        assert intake_state.client is None
        assert intake_state.case is None
        assert intake_state.current_task is None
        assert intake_state.task_status is None

    def test_set_client_info(self, intake_state: IntakeAgentState, client_info: ClientInfo):
        """Test setting client info."""
        intake_state.set_client_info(client_info)
        assert intake_state.client == client_info
        assert intake_state.client.name == "John Doe"
        assert intake_state.client.contact == "<EMAIL>"
        assert intake_state.client.injury_description == "Car accident injury"
        assert intake_state.client.incident_date == "2023-01-15"
        assert "ABC Insurance" in intake_state.client.opposing_parties
        assert "City Hospital" in intake_state.client.medical_providers

    def test_set_case_info(self, intake_state: IntakeAgentState, case_info: CaseInfo):
        """Test setting case info."""
        intake_state.set_case_info(case_info)
        assert intake_state.case == case_info
        assert intake_state.case.case_id == "case-123"
        assert intake_state.case.client_id == "client-456"
        assert intake_state.case.case_type == "Personal Injury"
        assert intake_state.case.status == "Active"
        assert len(intake_state.case.deadlines) == 1
        assert intake_state.case.deadlines[0]["description"] == "File complaint"
        assert len(intake_state.case.documents) == 1
        assert intake_state.case.documents[0]["name"] == "Police Report"


# ============================================================================
# Phase 3: Integration Tests
# ============================================================================

class TestStateFactory:
    """Integration tests for the state factory function."""

    def test_create_intake_state(self):
        """Test creating an intake agent state."""
        state = create_state(
            agent_type="intake",
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789"
        )
        assert isinstance(state, IntakeAgentState)
        assert state.tenant_id == "tenant-456"
        assert state.user_id == "user-123"
        assert state.thread_id == "thread-789"
        assert state.agent_type == "intake"

    def test_create_research_state(self):
        """Test creating a research agent state."""
        state = create_state(
            agent_type="research",
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            question="What is the statute of limitations for personal injury in Texas?"
        )
        assert isinstance(state, ResearchAgentState)
        assert state.tenant_id == "tenant-456"
        assert state.user_id == "user-123"
        assert state.thread_id == "thread-789"
        assert state.agent_type == "research"
        assert state.question == "What is the statute of limitations for personal injury in Texas?"
        assert state.jurisdiction == "texas"  # Default value

    def test_create_document_state(self):
        """Test creating a document agent state."""
        state = create_state(
            agent_type="document",
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            document_id="doc-123",
            document_type="Demand Letter"
        )
        assert isinstance(state, DocumentAgentState)
        assert state.tenant_id == "tenant-456"
        assert state.user_id == "user-123"
        assert state.thread_id == "thread-789"
        assert state.agent_type == "document"
        assert state.document_id == "doc-123"
        assert state.document_type == "Demand Letter"

    def test_create_deadline_state(self):
        """Test creating a deadline agent state."""
        state = create_state(
            agent_type="deadline",
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            document_text="The complaint must be filed within 30 days of the incident."
        )
        assert isinstance(state, DeadlineAgentState)
        assert state.tenant_id == "tenant-456"
        assert state.user_id == "user-123"
        assert state.thread_id == "thread-789"
        assert state.agent_type == "deadline"
        assert state.document_text == "The complaint must be filed within 30 days of the incident."
        assert state.jurisdiction == "texas"  # Default value

    def test_create_with_custom_user_context(self):
        """Test creating a state with a custom user context."""
        user_context = UserContext(
            user_id="user-123",
            tenant_id="tenant-456",
            role="partner",
            assigned_case_ids=["case-1", "case-2"],
            settings={"theme": "dark"}
        )
        
        state = create_state(
            agent_type="intake",
            tenant_id="tenant-456",
            user_id="user-123",
            thread_id="thread-789",
            user_context=user_context
        )
        
        assert state.user_context == user_context
        assert state.user_context.role == "partner"
        assert "case-1" in state.user_context.assigned_case_ids
        assert state.user_context.settings["theme"] == "dark"
