"""
Tests for checkpoint persistence in LangGraph state management.

This module tests the checkpoint persistence functionality of the LangGraph state management system.
"""

from typing import Any, Dict
from unittest.mock import patch

import pytest
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from src.pi_lawyer.state.langgraph_state import (
    LangGraphCheckpointSaver,
    StateManager,
    create_state,
    create_typed_state,
)


@pytest.mark.asyncio
class TestCheckpointPersistence:
    """Tests for checkpoint persistence."""

    @patch("src.pi_lawyer.state.langgraph_state.StateManager.save_state")
    @patch("src.pi_lawyer.state.langgraph_state.StateManager.load_state")
    async def test_save_and_load_checkpoint(self, mock_load_state, mock_save_state):
        """Test saving and loading a checkpoint."""
        # Configure the mocks
        mock_save_state.return_value = "record-123"

        # Create a state
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789",
            question="What is the statute of limitations for personal injury in Texas?"
        )

        # Add some messages and memory items
        state.add_message(SystemMessage(content="You are a legal research assistant."))
        state.add_message(HumanMessage(content="Can you research the statute of limitations for personal injury in Texas?"))
        state.add_message(AIMessage(content="I'll research that for you."))
        state.set_memory("search_query", "Texas personal injury statute of limitations")
        state.set_memory("search_results", ["Texas Civil Practice and Remedies Code § 16.003"])

        # Create a checkpoint saver
        saver = LangGraphCheckpointSaver("tenant-123")

        # Save the state
        await saver.put("thread-789", create_typed_state(state))

        # Verify that save_state was called
        mock_save_state.assert_called_once()

        # Configure the mock to return the state for loading
        mock_load_state.return_value = state

        # Load the state
        loaded_state_dict = await saver.get("thread-789")

        # Verify that load_state was called
        mock_load_state.assert_called_once_with("thread-789", "tenant-123")

        # Convert the loaded state dict to a state object
        loaded_state = StateManager.from_typed_dict(loaded_state_dict)

        # Verify that the loaded state has the correct values
        assert loaded_state.tenant_id == "tenant-123"
        assert loaded_state.user_id == "user-456"
        assert loaded_state.thread_id == "thread-789"
        assert loaded_state.agent_type == "research"
        assert loaded_state.question == "What is the statute of limitations for personal injury in Texas?"
        assert len(loaded_state.messages) == 3
        assert loaded_state.messages[0].content == "You are a legal research assistant."
        assert loaded_state.messages[1].content == "Can you research the statute of limitations for personal injury in Texas?"
        assert loaded_state.messages[2].content == "I'll research that for you."
        assert loaded_state.memory["search_query"] == "Texas personal injury statute of limitations"
        assert loaded_state.memory["search_results"] == ["Texas Civil Practice and Remedies Code § 16.003"]

    @patch("src.pi_lawyer.state.langgraph_state.StateManager.save_state")
    @patch("src.pi_lawyer.state.langgraph_state.StateManager.load_state")
    async def test_node_execution_with_loaded_state(self, mock_load_state, mock_save_state):
        """Test executing a node with a loaded state."""
        # Create a state
        state = create_state(
            agent_type="research",
            tenant_id="tenant-123",
            user_id="user-456",
            thread_id="thread-789",
            question="What is the statute of limitations for personal injury in Texas?"
        )

        # Add some messages and memory items
        state.add_message(SystemMessage(content="You are a legal research assistant."))
        state.add_message(HumanMessage(content="Can you research the statute of limitations for personal injury in Texas?"))

        # Configure the mock to return the state for loading
        mock_load_state.return_value = state

        # Create a checkpoint saver
        saver = LangGraphCheckpointSaver("tenant-123")

        # Load the state
        loaded_state_dict = await saver.get("thread-789")

        # Define a simple node function
        async def research_node(state_dict: Dict[str, Any]) -> Dict[str, Any]:
            """A simple research node."""
            return {
                "messages": [AIMessage(content="The statute of limitations for personal injury in Texas is 2 years.")],
                "memory": {"search_results": ["Texas Civil Practice and Remedies Code § 16.003"]}
            }

        # Execute the node with the loaded state
        node_result = await research_node(loaded_state_dict)

        # Apply the node result to the state
        from src.pi_lawyer.state.langgraph_state import add_messages, merge_dict

        updated_state_dict = loaded_state_dict.copy()
        updated_state_dict["messages"] = add_messages(loaded_state_dict["messages"], node_result["messages"])
        updated_state_dict["memory"] = merge_dict(loaded_state_dict["memory"], node_result["memory"])

        # Verify that the node execution worked correctly
        assert len(updated_state_dict["messages"]) == 3
        assert updated_state_dict["messages"][2].content == "The statute of limitations for personal injury in Texas is 2 years."
        assert updated_state_dict["memory"]["search_results"] == ["Texas Civil Practice and Remedies Code § 16.003"]

        # Configure the mock for the save
        mock_save_state.return_value = "record-123"

        # Save the updated state
        await saver.put("thread-789", updated_state_dict)

        # Verify that save_state was called
        mock_save_state.assert_called_once()
