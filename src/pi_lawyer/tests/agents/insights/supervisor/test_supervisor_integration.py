"""
Integration Tests for the Supervisor Agent

This module contains integration tests for the Supervisor Agent implementation,
focusing on its interaction with the StateGraph and other components.
"""

import json
from typing import Any, Dict
from unittest.mock import AsyncMock, patch

import pytest
from langgraph.graph import StateGraph

from pi_lawyer.agents.config import AgentConfig
from pi_lawyer.agents.insights.supervisor.agent import SupervisorAgent
from pi_lawyer.agents.insights.supervisor.router import route_to_agent


@pytest.fixture
def mock_llm_client():
    """Create a mock LLM client."""
    mock_client = AsyncMock()
    mock_client.chat_completion = AsyncMock()
    return mock_client


@pytest.fixture
def supervisor_agent(mock_llm_client):
    """Create a supervisor agent with a mock LLM client."""
    config = AgentConfig(
        name="test_supervisor_agent",
        agent_type="supervisor",
        description="Test supervisor agent",
        version="1.0.0"
    )
    return SupervisorAgent(config=config, llm_client=mock_llm_client)


@pytest.fixture
def mock_state_graph():
    """Create a mock StateGraph for testing."""
    # Create a simple StateGraph with just the supervisor and finish nodes
    sg = StateGraph(Dict[str, Any])

    # Add nodes
    sg.add_node("supervisorAgent", SupervisorAgent())
    sg.add_node("finish", lambda state, config: {"finished": True})

    # Add edges
    sg.add_conditional_edges(
        "supervisorAgent",
        lambda state: state.get("__command__", {}).get("goto", "finish"),
        {
            "FINISH": "finish"
        }
    )

    # Set entry point
    sg.set_entry_point("supervisorAgent")

    # Compile the graph
    return sg.compile()


@pytest.mark.asyncio
async def test_router_with_page_intent():
    """Test that the router correctly handles page_intent."""
    state = {
        "page_intent": "intakeAgent",
        "tenant_id": "test-tenant",
        "messages": []
    }

    result = route_to_agent(state)

    assert result == "intakeAgent"


@pytest.mark.asyncio
async def test_router_with_next():
    """Test that the router correctly handles next."""
    state = {
        "next": "researchAgent",
        "tenant_id": "test-tenant",
        "messages": []
    }

    result = route_to_agent(state)

    assert result == "researchAgent"


@pytest.mark.asyncio
async def test_router_default():
    """Test that the router defaults to supervisorAgent."""
    state = {
        "tenant_id": "test-tenant",
        "messages": []
    }

    result = route_to_agent(state)

    assert result == "supervisorAgent"


@pytest.mark.asyncio
async def test_router_with_string():
    """Test that the router handles string input."""
    result = route_to_agent("intakeAgent")

    assert result == "intakeAgent"


@pytest.mark.asyncio
async def test_tenant_isolation():
    """Test that the supervisor agent maintains tenant isolation."""
    # Create two supervisor agents with different tenant IDs
    config1 = AgentConfig(
        name="tenant1_supervisor",
        agent_type="supervisor",
        description="Tenant 1 supervisor agent",
        version="1.0.0"
    )

    config2 = AgentConfig(
        name="tenant2_supervisor",
        agent_type="supervisor",
        description="Tenant 2 supervisor agent",
        version="1.0.0"
    )

    # Create mock LLM clients
    mock_llm_client1 = AsyncMock()
    mock_llm_client1.chat_completion = AsyncMock()

    mock_llm_client2 = AsyncMock()
    mock_llm_client2.chat_completion = AsyncMock()

    # Create supervisor agents
    supervisor1 = SupervisorAgent(config=config1, llm_client=mock_llm_client1)
    supervisor2 = SupervisorAgent(config=config2, llm_client=mock_llm_client2)

    # Set up mock responses
    mock_llm_client1.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "function_call": {
                        "name": "classify_intent",
                        "arguments": json.dumps({
                            "agent": "researchAgent",
                            "args": {"query": "tenant1 query"},
                            "confidence": 0.95
                        })
                    }
                }
            }
        ]
    }

    mock_llm_client2.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "function_call": {
                        "name": "classify_intent",
                        "arguments": json.dumps({
                            "agent": "intakeAgent",
                            "args": {"client_name": "tenant2 client"},
                            "confidence": 0.9
                        })
                    }
                }
            }
        ]
    }

    # Create states for each tenant
    state1 = {
        "tenant_id": "tenant1",
        "messages": [{"type": "human", "content": "Research something for tenant1"}],
        "memory": {}
    }

    state2 = {
        "tenant_id": "tenant2",
        "messages": [{"type": "human", "content": "Add a new client for tenant2"}],
        "memory": {}
    }

    # Execute both agents
    result1 = await supervisor1.execute(state1, {})
    result2 = await supervisor2.execute(state2, {})

    # Verify tenant isolation
    assert result1.goto == "researchAgent"
    assert result2.goto == "intakeAgent"

    assert state1["agent_args"]["query"] == "tenant1 query"
    assert state2["agent_args"]["client_name"] == "tenant2 client"

    # Verify each LLM client was called with the correct tenant ID
    mock_llm_client1.chat_completion.assert_called_once()
    mock_llm_client2.chat_completion.assert_called_once()

    # The prompt should contain the tenant ID
    call_args1 = mock_llm_client1.chat_completion.call_args[1]
    call_args2 = mock_llm_client2.chat_completion.call_args[1]

    assert "tenant1" in call_args1["messages"][0]["content"]
    assert "tenant2" in call_args2["messages"][0]["content"]


@pytest.mark.asyncio
async def test_confidence_threshold_behavior():
    """Test that the confidence threshold behavior works correctly."""
    # Create a supervisor agent with a mock LLM client
    config = AgentConfig(
        name="test_supervisor_agent",
        agent_type="supervisor",
        description="Test supervisor agent",
        version="1.0.0"
    )

    mock_llm_client = AsyncMock()
    mock_llm_client.chat_completion = AsyncMock()

    # Create supervisor agent
    supervisor_agent = SupervisorAgent(config=config, llm_client=mock_llm_client)

    # Test with different confidence levels
    confidence_levels = [0.1, 0.3, 0.5, 0.7, 0.9]

    for confidence in confidence_levels:
        # Set up mock response
        mock_llm_client.chat_completion.return_value = {
            "choices": [
                {
                    "message": {
                        "function_call": {
                            "name": "classify_intent",
                            "arguments": json.dumps({
                                "agent": "researchAgent",
                                "args": {"query": "test query"},
                                "confidence": confidence
                            })
                        }
                    }
                }
            ]
        }

        # Create state
        state = {
            "tenant_id": "test-tenant",
            "messages": [{"type": "human", "content": "Test message"}],
            "memory": {}
        }

        # Set the confidence threshold environment variable
        with patch("os.getenv") as mock_getenv:
            mock_getenv.return_value = "0.6"  # Set threshold to 0.6

            # Execute the agent
            result = await supervisor_agent.execute(state, {})

            # Verify behavior based on confidence
            if confidence < 0.6:
                assert result.goto == "intakeAgent"  # Default agent
            else:
                assert result.goto == "researchAgent"
