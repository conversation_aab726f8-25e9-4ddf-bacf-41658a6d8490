"""
Performance and Error Handling Tests for the Supervisor Agent

This module contains performance and error handling tests for the Supervisor Agent implementation.
"""

import asyncio
import json
import time
from unittest.mock import AsyncMock, patch

import pytest

from pi_lawyer.agents.config import AgentConfig
from pi_lawyer.agents.insights.supervisor.agent import SupervisorAgent
from pi_lawyer.agents.insights.supervisor.schema import Classification


@pytest.fixture
def mock_llm_client():
    """Create a mock LLM client."""
    mock_client = AsyncMock()
    mock_client.chat_completion = AsyncMock()
    return mock_client


@pytest.fixture
def supervisor_agent(mock_llm_client):
    """Create a supervisor agent with a mock LLM client."""
    config = AgentConfig(
        name="test_supervisor_agent",
        agent_type="supervisor",
        description="Test supervisor agent",
        version="1.0.0"
    )
    return SupervisorAgent(config=config, llm_client=mock_llm_client)


@pytest.mark.asyncio
async def test_cache_performance(supervisor_agent, mock_llm_client):
    """Test that the caching mechanism improves performance."""
    # Set up the mock response
    mock_llm_client.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "function_call": {
                        "name": "classify_intent",
                        "arguments": json.dumps({
                            "agent": "researchAgent",
                            "args": {
                                "query": "statute of limitations",
                                "jurisdiction": "Texas"
                            },
                            "confidence": 0.95
                        })
                    }
                }
            }
        ]
    }
    
    # Create a state
    state = {
        "messages": [
            {"type": "human", "content": "Research the statute of limitations for personal injury in Texas"}
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }
    
    # First call (no cache)
    start_time = time.time()
    result1 = await supervisor_agent._classify_intent(state)
    first_call_time = time.time() - start_time
    
    # Second call (should use cache)
    start_time = time.time()
    result2 = await supervisor_agent._classify_intent(state)
    second_call_time = time.time() - start_time
    
    # Verify that the second call was faster
    assert second_call_time < first_call_time
    
    # Verify that the LLM was only called once
    assert mock_llm_client.chat_completion.call_count == 1
    
    # Verify that the results are the same
    assert result1.agent == result2.agent
    assert result1.args == result2.args
    assert result1.confidence == result2.confidence


@pytest.mark.asyncio
async def test_cache_with_different_tenants(supervisor_agent, mock_llm_client):
    """Test that the cache respects tenant isolation."""
    # Set up the mock response
    mock_llm_client.chat_completion.side_effect = [
        # First response (tenant1)
        {
            "choices": [
                {
                    "message": {
                        "function_call": {
                            "name": "classify_intent",
                            "arguments": json.dumps({
                                "agent": "researchAgent",
                                "args": {"query": "tenant1 query"},
                                "confidence": 0.95
                            })
                        }
                    }
                }
            ]
        },
        # Second response (tenant2)
        {
            "choices": [
                {
                    "message": {
                        "function_call": {
                            "name": "classify_intent",
                            "arguments": json.dumps({
                                "agent": "intakeAgent",
                                "args": {"client_name": "tenant2 client"},
                                "confidence": 0.9
                            })
                        }
                    }
                }
            ]
        }
    ]
    
    # Create states for each tenant
    state1 = {
        "messages": [
            {"type": "human", "content": "Same message for both tenants"}
        ],
        "tenant_id": "tenant1",
        "memory": {}
    }
    
    state2 = {
        "messages": [
            {"type": "human", "content": "Same message for both tenants"}
        ],
        "tenant_id": "tenant2",
        "memory": {}
    }
    
    # Call with tenant1
    result1 = await supervisor_agent._classify_intent(state1)
    
    # Call with tenant2
    result2 = await supervisor_agent._classify_intent(state2)
    
    # Verify that the LLM was called twice (once for each tenant)
    assert mock_llm_client.chat_completion.call_count == 2
    
    # Verify that the results are different
    assert result1.agent == "researchAgent"
    assert result2.agent == "intakeAgent"
    
    # Call with tenant1 again (should use cache)
    result3 = await supervisor_agent._classify_intent(state1)
    
    # Verify that the LLM was still called only twice
    assert mock_llm_client.chat_completion.call_count == 2
    
    # Verify that the result is the same as the first call
    assert result3.agent == result1.agent
    assert result3.args == result1.args


@pytest.mark.asyncio
async def test_concurrent_requests(supervisor_agent, mock_llm_client):
    """Test that the supervisor agent can handle concurrent requests."""
    # Set up the mock response
    mock_llm_client.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "function_call": {
                        "name": "classify_intent",
                        "arguments": json.dumps({
                            "agent": "researchAgent",
                            "args": {"query": "test query"},
                            "confidence": 0.95
                        })
                    }
                }
            }
        ]
    }
    
    # Create 10 different states
    states = []
    for i in range(10):
        states.append({
            "messages": [
                {"type": "human", "content": f"Test message {i}"}
            ],
            "tenant_id": f"tenant{i}",
            "memory": {}
        })
    
    # Execute all requests concurrently
    start_time = time.time()
    results = await asyncio.gather(*[supervisor_agent._classify_intent(state) for state in states])
    total_time = time.time() - start_time
    
    # Verify that all requests were processed
    assert len(results) == 10
    
    # Verify that all results are valid
    for result in results:
        assert isinstance(result, Classification)
        assert result.agent == "researchAgent"
        assert result.confidence == 0.95
    
    # Verify that the LLM was called 10 times
    assert mock_llm_client.chat_completion.call_count == 10
    
    # Average time per request should be reasonable
    avg_time = total_time / 10
    assert avg_time < 0.1  # Less than 100ms per request on average


@pytest.mark.asyncio
async def test_llm_failure_recovery(supervisor_agent, mock_llm_client):
    """Test that the supervisor agent recovers from LLM failures."""
    # Set up the mock response to fail on first call, succeed on second
    mock_llm_client.chat_completion.side_effect = [
        Exception("LLM service unavailable"),
        {
            "choices": [
                {
                    "message": {
                        "function_call": {
                            "name": "classify_intent",
                            "arguments": json.dumps({
                                "agent": "researchAgent",
                                "args": {"query": "test query"},
                                "confidence": 0.95
                            })
                        }
                    }
                }
            ]
        }
    ]
    
    # Create a state
    state = {
        "messages": [
            {"type": "human", "content": "Test message"}
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }
    
    # This should not raise an exception
    result = await supervisor_agent._classify_intent(state)
    
    # Verify that the result is the default
    assert result.agent == "intakeAgent"  # Default agent
    assert result.confidence == 0.0


@pytest.mark.asyncio
async def test_malformed_llm_response(supervisor_agent, mock_llm_client):
    """Test that the supervisor agent handles malformed LLM responses."""
    # Set up various malformed responses
    malformed_responses = [
        # Empty response
        {},
        # Missing choices
        {"choices": []},
        # Missing message
        {"choices": [{}]},
        # Invalid JSON in function call
        {
            "choices": [
                {
                    "message": {
                        "function_call": {
                            "name": "classify_intent",
                            "arguments": "{invalid json"
                        }
                    }
                }
            ]
        },
        # Missing required fields in function call
        {
            "choices": [
                {
                    "message": {
                        "function_call": {
                            "name": "classify_intent",
                            "arguments": json.dumps({
                                "args": {"query": "test query"}
                                # Missing agent and confidence
                            })
                        }
                    }
                }
            ]
        },
        # Invalid content (not JSON)
        {
            "choices": [
                {
                    "message": {
                        "content": "This is not JSON"
                    }
                }
            ]
        }
    ]
    
    # Create a state
    state = {
        "messages": [
            {"type": "human", "content": "Test message"}
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }
    
    # Test each malformed response
    for response in malformed_responses:
        mock_llm_client.chat_completion.return_value = response
        
        # This should not raise an exception
        result = await supervisor_agent._classify_intent(state)
        
        # Verify that the result is the default
        assert result.agent == "intakeAgent"  # Default agent
        assert result.confidence == 0.0


@pytest.mark.asyncio
async def test_async_job_failure(supervisor_agent):
    """Test that the supervisor agent handles async job failures."""
    # Mock the _classify_intent method
    supervisor_agent._classify_intent = AsyncMock(return_value=Classification(
        agent="documentDraftAgent",
        args={"template": "demand_letter", "matter_id": "case-123"},
        confidence=0.9
    ))
    
    # Mock the enqueue_async_job_tool to fail
    with patch("pi_lawyer.agents.insights.supervisor.agent.enqueue_async_job_tool") as mock_tool:
        mock_tool.execute = AsyncMock(side_effect=Exception("Failed to enqueue job"))
        
        state = {
            "messages": [
                {"type": "human", "content": "Draft a demand letter for case 123"}
            ],
            "tenant_id": "test-tenant",
            "user_id": "user-456",
            "thread_id": "thread-789",
            "matter_id": "case-123",
            "memory": {}
        }
        
        # This should not raise an exception
        result = await supervisor_agent.execute(state, {})
        
        # Verify that the result is FINISH
        assert result.goto == "FINISH"
        
        # Verify that an error message was added
        assert any(m.get("type") == "ai" and "error" in m.get("content", "").lower() for m in state["messages"])
