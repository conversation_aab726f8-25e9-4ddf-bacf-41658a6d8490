"""
Tests for the Supervisor Agent

This module contains tests for the Supervisor Agent implementation.
"""

import json
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from hypothesis import given
from hypothesis import strategies as st
from langgraph.types import Command

from pi_lawyer.agents.config import AgentConfig
from pi_lawyer.agents.insights.supervisor.agent import SupervisorAgent
from pi_lawyer.agents.insights.supervisor.schema import Classification


@pytest.fixture
def mock_llm_client():
    """Create a mock LLM client."""
    mock_client = AsyncMock()
    mock_client.chat_completion = AsyncMock()
    return mock_client


@pytest.fixture
def supervisor_agent(mock_llm_client):
    """Create a supervisor agent with a mock LLM client."""
    config = AgentConfig(
        name="test_supervisor_agent",
        agent_type="supervisor",
        description="Test supervisor agent",
        version="1.0.0"
    )
    return SupervisorAgent(config=config, llm_client=mock_llm_client)


@pytest.mark.asyncio
async def test_initialize(supervisor_agent):
    """Test that the supervisor agent initializes state correctly."""
    state = {
        "messages": [],
        "memory": {}
    }

    result = await supervisor_agent.initialize(state, {})

    assert "messages" in result
    assert any(m.get("type") == "system" for m in result["messages"])
    assert "memory" in result
    assert "initialized_at" in result["memory"]
    assert result["memory"]["agent_name"] == "test_supervisor_agent"


@pytest.mark.asyncio
async def test_classify_intent_function_call(supervisor_agent, mock_llm_client):
    """Test that the supervisor agent classifies intent correctly using function calling."""
    # Set up the mock response
    mock_llm_client.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "function_call": {
                        "name": "classify_intent",
                        "arguments": json.dumps({
                            "agent": "researchAgent",
                            "args": {
                                "query": "statute of limitations",
                                "jurisdiction": "Texas"
                            },
                            "confidence": 0.95
                        })
                    }
                }
            }
        ]
    }

    state = {
        "messages": [
            {"type": "human", "content": "Research the statute of limitations for personal injury in Texas"}
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }

    result = await supervisor_agent._classify_intent(state)

    assert isinstance(result, Classification)
    assert result.agent == "researchAgent"
    assert "query" in result.args
    assert result.confidence == 0.95

    # Verify the LLM was called with the right parameters
    mock_llm_client.chat_completion.assert_called_once()
    call_args = mock_llm_client.chat_completion.call_args[1]
    assert call_args["model"] == "voyage-large"
    assert call_args["temperature"] == 0.2
    assert "functions" in call_args


@pytest.mark.asyncio
async def test_classify_intent_with_empty_messages(supervisor_agent, mock_llm_client):
    """Test that the supervisor agent handles empty messages correctly."""
    state = {
        "messages": [],
        "tenant_id": "test-tenant",
        "memory": {}
    }

    result = await supervisor_agent._classify_intent(state)

    assert isinstance(result, Classification)
    assert result.agent == "intakeAgent"  # Default agent
    assert result.confidence == 0.0

    # Verify the LLM was not called
    mock_llm_client.chat_completion.assert_not_called()


@pytest.mark.asyncio
async def test_classify_intent_with_non_human_messages(supervisor_agent, mock_llm_client):
    """Test that the supervisor agent handles non-human messages correctly."""
    state = {
        "messages": [
            {"type": "system", "content": "System message"},
            {"type": "ai", "content": "AI message"}
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }

    result = await supervisor_agent._classify_intent(state)

    assert isinstance(result, Classification)
    assert result.agent == "intakeAgent"  # Default agent
    assert result.confidence == 0.0

    # Verify the LLM was not called
    mock_llm_client.chat_completion.assert_not_called()


@pytest.mark.asyncio
async def test_classify_intent_with_matter_id(supervisor_agent, mock_llm_client):
    """Test that the supervisor agent includes matter_id in the prompt."""
    # Set up the mock response
    mock_llm_client.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "function_call": {
                        "name": "classify_intent",
                        "arguments": json.dumps({
                            "agent": "researchAgent",
                            "args": {
                                "query": "statute of limitations",
                                "jurisdiction": "Texas",
                                "matter_id": "case-123"
                            },
                            "confidence": 0.95
                        })
                    }
                }
            }
        ]
    }

    state = {
        "messages": [
            {"type": "human", "content": "Research the statute of limitations for personal injury in Texas"}
        ],
        "tenant_id": "test-tenant",
        "matter_id": "case-123",
        "memory": {}
    }

    result = await supervisor_agent._classify_intent(state)

    assert isinstance(result, Classification)
    assert result.agent == "researchAgent"
    assert result.args.get("matter_id") == "case-123"

    # Verify the LLM was called with the right parameters
    mock_llm_client.chat_completion.assert_called_once()
    call_args = mock_llm_client.chat_completion.call_args[1]

    # The prompt should contain the matter_id
    assert "case-123" in call_args["messages"][0]["content"]


@pytest.mark.asyncio
async def test_classify_intent_fallback(supervisor_agent, mock_llm_client):
    """Test that the supervisor agent falls back to content parsing if function calling fails."""
    # Set up the mock response with no function call
    mock_llm_client.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "content": json.dumps({
                        "agent": "intakeAgent",
                        "args": {},
                        "confidence": 0.8
                    })
                }
            }
        ]
    }

    state = {
        "messages": [
            {"type": "human", "content": "I need to add a new client"}
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }

    result = await supervisor_agent._classify_intent(state)

    assert isinstance(result, Classification)
    assert result.agent == "intakeAgent"
    assert result.confidence == 0.8


@pytest.mark.asyncio
async def test_execute_interactive_agent(supervisor_agent):
    """Test that the supervisor agent routes to interactive agents correctly."""
    # Mock the _classify_intent method
    supervisor_agent._classify_intent = AsyncMock(return_value=Classification(
        agent="researchAgent",
        args={"query": "statute of limitations", "jurisdiction": "Texas"},
        confidence=0.95
    ))

    state = {
        "messages": [
            {"type": "human", "content": "Research the statute of limitations for personal injury in Texas"}
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }

    result = await supervisor_agent.execute(state, {})

    assert isinstance(result, Command)
    assert result.goto == "researchAgent"
    assert state["agent_args"] == {"query": "statute of limitations", "jurisdiction": "Texas"}


@pytest.mark.asyncio
async def test_execute_async_agent(supervisor_agent):
    """Test that the supervisor agent handles async agents correctly."""
    # Mock the _classify_intent method
    supervisor_agent._classify_intent = AsyncMock(return_value=Classification(
        agent="documentDraftAgent",
        args={"template": "demand_letter", "matter_id": "case-123"},
        confidence=0.9
    ))

    # Mock the enqueue_async_job_tool
    with patch("pi_lawyer.agents.insights.supervisor.agent.enqueue_async_job_tool") as mock_tool:
        mock_tool.execute = AsyncMock(return_value=MagicMock(job_id="job-123"))

        state = {
            "messages": [
                {"type": "human", "content": "Draft a demand letter for case 123"}
            ],
            "tenant_id": "test-tenant",
            "user_id": "user-456",
            "thread_id": "thread-789",
            "matter_id": "case-123",
            "memory": {}
        }

        result = await supervisor_agent.execute(state, {})

        assert isinstance(result, Command)
        assert result.goto == "FINISH"
        assert state["async_job_id"] == "job-123"
        assert any(m.get("type") == "ai" for m in state["messages"])

        # Verify the tool was called with the right parameters
        mock_tool.execute.assert_called_once()
        call_args = mock_tool.execute.call_args[1]
        assert call_args["tool_name"] == "documentDraftAgent"
        assert call_args["tenant_id"] == "test-tenant"
        assert call_args["matter_id"] == "case-123"


@pytest.mark.asyncio
async def test_execute_low_confidence(supervisor_agent):
    """Test that the supervisor agent handles low confidence correctly."""
    # Mock the _classify_intent method
    supervisor_agent._classify_intent = AsyncMock(return_value=Classification(
        agent="researchAgent",
        args={"query": "something unclear"},
        confidence=0.3  # Below threshold
    ))

    state = {
        "messages": [
            {"type": "human", "content": "Something unclear"}
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }

    result = await supervisor_agent.execute(state, {})

    assert isinstance(result, Command)
    assert result.goto == "intakeAgent"  # Default agent
    assert state["agent_args"] == {}


@pytest.mark.asyncio
async def test_cleanup_method(supervisor_agent):
    """Test that the cleanup method works correctly."""
    state = {
        "messages": [
            {"type": "human", "content": "Test message"}
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }

    result = await supervisor_agent.cleanup(state, {})

    assert "memory" in result
    assert "cleaned_up_at" in result["memory"]


@pytest.mark.asyncio
@given(
    user_message=st.text(min_size=1, max_size=1000),
    tenant_id=st.text(min_size=1, max_size=36),
    matter_id=st.one_of(st.none(), st.text(min_size=1, max_size=36))
)
async def test_classify_intent_fuzz(supervisor_agent, mock_llm_client, user_message, tenant_id, matter_id):
    """
    Property-based fuzz test for the _classify_intent method.

    This test ensures that the method never throws an exception regardless of input.
    """
    # Set up the mock response to simulate various LLM responses
    mock_responses = [
        # Valid function call response
        {
            "choices": [
                {
                    "message": {
                        "function_call": {
                            "name": "classify_intent",
                            "arguments": json.dumps({
                                "agent": "researchAgent",
                                "args": {"query": user_message[:100]},
                                "confidence": 0.9
                            })
                        }
                    }
                }
            ]
        },
        # Valid content response
        {
            "choices": [
                {
                    "message": {
                        "content": json.dumps({
                            "agent": "intakeAgent",
                            "args": {},
                            "confidence": 0.8
                        })
                    }
                }
            ]
        },
        # Invalid JSON response
        {
            "choices": [
                {
                    "message": {
                        "content": "I'm not sure what you're asking for."
                    }
                }
            ]
        },
        # Empty response
        {
            "choices": []
        },
        # Error response (will be caught)
        None
    ]

    # Randomly select one of the mock responses
    import random
    mock_llm_client.chat_completion.side_effect = [
        mock_responses[random.randint(0, len(mock_responses) - 1)],
        # Fallback to a valid response if the first one fails
        mock_responses[0]
    ]

    state = {
        "messages": [
            {"type": "human", "content": user_message}
        ],
        "tenant_id": tenant_id,
        "memory": {}
    }

    if matter_id:
        state["matter_id"] = matter_id

    try:
        # This should never throw an exception
        result = await supervisor_agent._classify_intent(state)

        # Verify the result is a Classification object
        assert isinstance(result, Classification)

        # Verify the agent field is one of the expected values
        assert result.agent in [
            "intakeAgent", "researchAgent", "taskCrudAgent",
            "calendarCrudAgent", "documentDraftAgent", "insightSwarmAgent"
        ]

        # Verify the confidence is between 0 and 1
        assert 0 <= result.confidence <= 1

    except Exception as e:
        pytest.fail(f"_classify_intent raised an exception: {str(e)}")
