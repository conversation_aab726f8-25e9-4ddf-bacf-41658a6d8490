"""
Tests for the mode-specific configuration

This module contains tests for the mode-specific configuration used by the Intake Agent.
"""

import importlib
from pathlib import Path
from unittest.mock import AsyncMock, patch

import pytest

from pi_lawyer.agents.interactive.intake.agent import IntakeAgent


class TestModeConfig:
    """Tests for the mode-specific configuration."""

    def test_client_config_exists(self):
        """Test that the client configuration exists."""
        try:
            config = importlib.import_module("pi_lawyer.agents.interactive.intake.client_flow.config")
            assert hasattr(config, "ALLOWED_TOOLS")
            assert isinstance(config.ALLOWED_TOOLS, list)
        except ImportError:
            pytest.fail("Client configuration module not found")

    def test_staff_config_exists(self):
        """Test that the staff configuration exists."""
        try:
            config = importlib.import_module("pi_lawyer.agents.interactive.intake.staff_flow.config")
            assert hasattr(config, "ALLOWED_TOOLS")
            assert isinstance(config.ALLOWED_TOOLS, list)
        except ImportError:
            pytest.fail("Staff configuration module not found")

    def test_client_prompt_exists(self):
        """Test that the client prompt exists."""
        prompt_path = Path(__file__).parent.parent.parent.parent.parent / "agents/interactive/intake/client_flow/prompt.md"
        assert prompt_path.exists()
        with open(prompt_path, "r") as f:
            content = f.read()
            assert len(content) > 0
            assert "AiLex" in content

    def test_staff_prompt_exists(self):
        """Test that the staff prompt exists."""
        prompt_path = Path(__file__).parent.parent.parent.parent.parent / "agents/interactive/intake/staff_flow/prompt.md"
        assert prompt_path.exists()
        with open(prompt_path, "r") as f:
            content = f.read()
            assert len(content) > 0
            assert "AiLex" in content

    def test_client_tools_subset_of_staff_tools(self):
        """Test that the client tools are a subset of the staff tools."""
        client_config = importlib.import_module("pi_lawyer.agents.interactive.intake.client_flow.config")
        staff_config = importlib.import_module("pi_lawyer.agents.interactive.intake.staff_flow.config")
        
        # Check that all client tools are also in staff tools
        for tool in client_config.ALLOWED_TOOLS:
            assert tool in staff_config.ALLOWED_TOOLS
            
        # Check that staff has more tools than client
        assert len(staff_config.ALLOWED_TOOLS) > len(client_config.ALLOWED_TOOLS)

    @pytest.mark.asyncio
    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    @patch("pi_lawyer.agents.interactive.intake.agent.importlib.import_module")
    async def test_agent_loads_client_config(self, mock_import_module, mock_voyage_client):
        """Test that the agent loads the client configuration."""
        # Arrange
        mock_config = type('MockConfig', (), {"ALLOWED_TOOLS": ["collect_client_info", "check_conflicts"]})
        mock_import_module.return_value = mock_config
        
        agent = IntakeAgent()
        agent.llm_client = AsyncMock()
        
        # Act
        state = {
            "messages": [],
            "intake_mode": "client"
        }
        config = {}
        result = await agent.initialize(state, config)
        
        # Assert
        assert agent.allowed_tools == ["collect_client_info", "check_conflicts"]
        mock_import_module.assert_called_with("pi_lawyer.agents.interactive.intake.client_flow.config")

    @pytest.mark.asyncio
    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    @patch("pi_lawyer.agents.interactive.intake.agent.importlib.import_module")
    async def test_agent_loads_staff_config(self, mock_import_module, mock_voyage_client):
        """Test that the agent loads the staff configuration."""
        # Arrange
        mock_config = type('MockConfig', (), {"ALLOWED_TOOLS": [
            "collect_client_info", 
            "check_conflicts", 
            "create_client", 
            "create_case"
        ]})
        mock_import_module.return_value = mock_config
        
        agent = IntakeAgent()
        agent.llm_client = AsyncMock()
        
        # Act
        state = {
            "messages": [],
            "intake_mode": "staff"
        }
        config = {}
        result = await agent.initialize(state, config)
        
        # Assert
        assert agent.allowed_tools == [
            "collect_client_info", 
            "check_conflicts", 
            "create_client", 
            "create_case"
        ]
        mock_import_module.assert_called_with("pi_lawyer.agents.interactive.intake.staff_flow.config")
