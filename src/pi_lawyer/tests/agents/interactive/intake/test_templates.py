"""
Tests for the practice area templates

This module contains tests for the practice area templates used by the Intake Agent.
"""

from pathlib import Path
from unittest.mock import AsyncMock, patch

import pytest
import yaml

from pi_lawyer.agents.interactive.intake.agent import IntakeAgent


class TestPracticeAreaTemplates:
    """Tests for the practice area templates."""

    @pytest.fixture
    def template_dir(self) -> Path:
        """Get the template directory."""
        return Path(__file__).parent.parent.parent.parent.parent / "shared/intake_templates"

    def test_template_files_exist(self, template_dir: Path):
        """Test that the template files exist."""
        assert (template_dir / "default.yaml").exists()
        assert (template_dir / "personal_injury.yaml").exists()
        assert (template_dir / "criminal_defense.yaml").exists()
        assert (template_dir / "family_law.yaml").exists()

    def test_template_structure(self, template_dir: Path):
        """Test that the templates have the correct structure."""
        for template_file in ["default.yaml", "personal_injury.yaml", "criminal_defense.yaml", "family_law.yaml"]:
            with open(template_dir / template_file, "r") as f:
                template = yaml.safe_load(f)
                
                # Check required fields
                assert "title" in template
                assert "fields" in template
                assert isinstance(template["fields"], list)
                
                # Check field structure
                for field in template["fields"]:
                    assert "id" in field
                    assert "prompt" in field
                    assert "type" in field or "required" in field

    @pytest.mark.asyncio
    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    async def test_load_practice_area_template(self, mock_voyage_client):
        """Test loading a practice area template."""
        # Arrange
        agent = IntakeAgent()
        agent.llm_client = AsyncMock()
        
        # Act
        template = await agent._load_practice_area_template("personal_injury", "test-tenant")
        
        # Assert
        assert template is not None
        assert "title" in template
        assert template["title"] == "Personal Injury Intake"
        assert "fields" in template
        assert len(template["fields"]) > 0
        
        # Check that required fields are present
        field_ids = [field["id"] for field in template["fields"]]
        assert "name" in field_ids
        assert "email" in field_ids
        assert "phone" in field_ids
        assert "injury_description" in field_ids

    @pytest.mark.asyncio
    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    async def test_load_criminal_defense_template(self, mock_voyage_client):
        """Test loading the criminal defense template."""
        # Arrange
        agent = IntakeAgent()
        agent.llm_client = AsyncMock()
        
        # Act
        template = await agent._load_practice_area_template("criminal_defense", "test-tenant")
        
        # Assert
        assert template is not None
        assert "title" in template
        assert template["title"] == "Criminal Defense Intake"
        assert "fields" in template
        assert len(template["fields"]) > 0
        
        # Check that required fields are present
        field_ids = [field["id"] for field in template["fields"]]
        assert "name" in field_ids
        assert "email" in field_ids
        assert "phone" in field_ids
        assert "charge_description" in field_ids

    @pytest.mark.asyncio
    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    async def test_load_family_law_template(self, mock_voyage_client):
        """Test loading the family law template."""
        # Arrange
        agent = IntakeAgent()
        agent.llm_client = AsyncMock()
        
        # Act
        template = await agent._load_practice_area_template("family_law", "test-tenant")
        
        # Assert
        assert template is not None
        assert "title" in template
        assert template["title"] == "Family Law Intake"
        assert "fields" in template
        assert len(template["fields"]) > 0
        
        # Check that required fields are present
        field_ids = [field["id"] for field in template["fields"]]
        assert "name" in field_ids
        assert "email" in field_ids
        assert "phone" in field_ids
        assert "case_type" in field_ids

    @pytest.mark.asyncio
    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    async def test_load_default_template_for_unknown_practice_area(self, mock_voyage_client):
        """Test loading the default template for an unknown practice area."""
        # Arrange
        agent = IntakeAgent()
        agent.llm_client = AsyncMock()
        
        # Act
        template = await agent._load_practice_area_template("unknown_practice_area", "test-tenant")
        
        # Assert
        assert template is not None
        assert "title" in template
        assert "fields" in template
        assert len(template["fields"]) > 0
