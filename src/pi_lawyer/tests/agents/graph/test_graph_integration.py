"""
Tests for the StateGraph integration with the Intake Agent

This module contains tests for the integration of the Intake Agent with the StateGraph.
"""

from unittest.mock import AsyncMock, patch

import pytest

from pi_lawyer.agents.graph.builder_update import build_graph


class TestGraphIntegration:
    """Tests for the StateGraph integration."""

    @pytest.mark.asyncio
    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    @patch("pi_lawyer.agents.interactive.intake.tasks.get_tool_executor")
    @patch("pi_lawyer.agents.insights.supervisor.agent.VoyageClient")
    async def test_client_intake_flow(self, mock_supervisor_voyage, mock_get_tool_executor, mock_intake_voyage):
        """Test the client intake flow through the graph."""
        # Arrange
        mock_intake_voyage.return_value = AsyncMock()
        mock_intake_voyage.return_value.chat_completion.side_effect = [
            {
                "choices": [
                    {
                        "message": {
                            "content": "I'll help you with the intake process. Next step: collect_personal_info"
                        }
                    }
                ]
            },
            {
                "choices": [
                    {
                        "message": {
                            "content": "I'll help you with the intake process. Next step: FINISH"
                        }
                    }
                ]
            }
        ]

        mock_supervisor_voyage.return_value = AsyncMock()

        mock_tool_executor = AsyncMock()
        mock_tool_executor.execute_tool.return_value = {"id": "test-id"}
        mock_get_tool_executor.return_value = mock_tool_executor

        # Build the graph with the client intake entry point
        graph = build_graph(entry="intakeClient")

        # Initial state
        state = {
            "messages": [{"type": "human", "content": "I need to submit a new case"}],
            "client": {},
            "case": {},
            "memory": {},
        }
        config = {"configurable": {"thread_id": "test-thread", "tenant_id": "test-tenant", "user_id": "test-user"}}

        # Act
        result = await graph.ainvoke(state, config)

        # Assert
        assert "intake_mode" in result
        assert result["intake_mode"] == "client"
        assert len(result["messages"]) > 1
        assert "next" in result
        assert result["next"] == "FINISH"

    @pytest.mark.asyncio
    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    @patch("pi_lawyer.agents.interactive.intake.tasks.get_tool_executor")
    @patch("pi_lawyer.agents.insights.supervisor.agent.VoyageClient")
    async def test_staff_intake_flow(self, mock_supervisor_voyage, mock_get_tool_executor, mock_intake_voyage):
        """Test the staff intake flow through the graph."""
        # Arrange
        mock_intake_voyage.return_value = AsyncMock()
        mock_intake_voyage.return_value.chat_completion.side_effect = [
            {
                "choices": [
                    {
                        "message": {
                            "content": "I'll help you with the intake process. Next step: summarize_and_confirm"
                        }
                    }
                ]
            },
            {
                "choices": [
                    {
                        "message": {
                            "content": "Here's a summary of the information. Next step: FINISH"
                        }
                    }
                ]
            }
        ]

        mock_supervisor_voyage.return_value = AsyncMock()

        mock_tool_executor = AsyncMock()
        mock_tool_executor.execute_tool.return_value = {"id": "test-id"}
        mock_get_tool_executor.return_value = mock_tool_executor

        # Build the graph with the staff intake entry point
        graph = build_graph(entry="intakeStaff")

        # Initial state with pre-filled data
        state = {
            "messages": [{"type": "human", "content": "I'm submitting a new case for John Doe"}],
            "client": {"name": "John Doe", "email": "<EMAIL>", "phone": "************"},
            "case": {"description": "Car accident", "case_type": "auto_accident", "practice_area": "personal_injury"},
            "memory": {},
        }
        config = {"configurable": {"thread_id": "test-thread", "tenant_id": "test-tenant", "user_id": "test-user"}}

        # Act
        result = await graph.ainvoke(state, config)

        # Assert
        assert "intake_mode" in result
        assert result["intake_mode"] == "staff"
        assert len(result["messages"]) > 1
        assert "next" in result
        assert result["next"] == "FINISH"

    @pytest.mark.asyncio
    @patch("pi_lawyer.agents.interactive.intake.agent.VoyageClient")
    @patch("pi_lawyer.agents.interactive.intake.tasks.get_tool_executor")
    @patch("pi_lawyer.agents.insights.supervisor.agent.VoyageClient")
    async def test_staff_intake_skip_to_confirm(self, mock_supervisor_voyage, mock_get_tool_executor, mock_intake_voyage):
        """Test that staff intake can skip to confirmation with pre-filled data."""
        # Arrange
        mock_intake_voyage.return_value = AsyncMock()
        mock_intake_voyage.return_value.chat_completion.side_effect = [
            {
                "choices": [
                    {
                        "message": {
                            "content": "I see you already have client information. Let me summarize it for you. Next step: summarize_and_confirm"
                        }
                    }
                ]
            },
            {
                "choices": [
                    {
                        "message": {
                            "content": "Here's a summary of the information. Next step: save_client_info"
                        }
                    }
                ]
            },
            {
                "choices": [
                    {
                        "message": {
                            "content": "Information saved successfully. Next step: FINISH"
                        }
                    }
                ]
            }
        ]

        mock_supervisor_voyage.return_value = AsyncMock()

        mock_tool_executor = AsyncMock()
        mock_tool_executor.execute_tool.return_value = {"id": "test-id"}
        mock_get_tool_executor.return_value = mock_tool_executor

        # Build the graph with the staff intake entry point
        graph = build_graph(entry="intakeStaff")

        # Initial state with pre-filled data
        state = {
            "messages": [{"type": "human", "content": "I'm submitting a new case for John Doe"}],
            "client": {"name": "John Doe", "email": "<EMAIL>", "phone": "************"},
            "case": {"description": "Car accident", "case_type": "auto_accident", "practice_area": "personal_injury"},
            "memory": {},
        }
        config = {"configurable": {"thread_id": "test-thread", "tenant_id": "test-tenant", "user_id": "test-user"}}

        # Act
        result = await graph.ainvoke(state, config)

        # Assert
        assert "intake_mode" in result
        assert result["intake_mode"] == "staff"
        assert "next" in result
        # The next step should be FINISH after all the steps
        assert result["next"] == "FINISH"
