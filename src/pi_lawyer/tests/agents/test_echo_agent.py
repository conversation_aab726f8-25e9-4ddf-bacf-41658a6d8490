"""
Tests for the Echo Agent

This module contains tests for the Echo Agent implementation.
"""

import json
from datetime import datetime

import pytest

from src.pi_lawyer.agents.echo_agent import SimpleEchoAgent


@pytest.fixture
def echo_agent():
    """Create an echo agent for testing."""
    return SimpleEchoAgent(name="test_echo_agent")

@pytest.mark.asyncio
async def test_echo_agent_initialization(echo_agent):
    """Test that the echo agent initializes correctly."""
    assert echo_agent.name == "test_echo_agent"
    assert echo_agent.start_time is not None
    # Verify the start time is a valid ISO format datetime string
    datetime.fromisoformat(echo_agent.start_time)

@pytest.mark.asyncio
async def test_echo_agent_handle_request_empty_message(echo_agent):
    """Test that the echo agent handles requests with empty messages."""
    request_data = {
        "messages": [],
        "threadId": "test-thread-123",
    }
    request_id = "req-123"

    response = await echo_agent.handle_request(request_data, request_id)

    assert response["messages"][0]["role"] == "assistant"
    assert "echo agent" in response["messages"][0]["content"].lower()
    assert response["done"] is True
    assert response["threadId"] == "test-thread-123"
    assert response["metadata"]["agent_name"] == "test_echo_agent"
    assert response["metadata"]["request_id"] == "req-123"
    assert response["metadata"]["thread_id"] == "test-thread-123"
    assert "timestamp" in response["metadata"]
    assert "processing_time_ms" in response["metadata"]
    assert "authenticated" in response["metadata"]
    assert "development_mode" in response["metadata"]

@pytest.mark.asyncio
async def test_echo_agent_handle_request_with_message(echo_agent):
    """Test that the echo agent echoes back messages."""
    request_data = {
        "messages": [
            {"role": "user", "content": "Hello, echo agent!"}
        ],
        "threadId": "test-thread-456",
    }
    request_id = "req-456"

    response = await echo_agent.handle_request(request_data, request_id)

    assert response["messages"][0]["role"] == "assistant"
    assert "Echo: Hello, echo agent!" in response["messages"][0]["content"]
    assert response["done"] is True
    assert response["threadId"] == "test-thread-456"
    assert response["metadata"]["agent_name"] == "test_echo_agent"
    assert response["metadata"]["request_id"] == "req-456"

@pytest.mark.asyncio
async def test_echo_agent_handle_request_with_auth(echo_agent):
    """Test that the echo agent includes authentication info when provided."""
    request_data = {
        "messages": [
            {"role": "user", "content": "Hello with auth!"}
        ],
        "threadId": "test-thread-789",
    }
    request_id = "req-789"
    auth_info = {
        "user_id": "user-123",
        "tenant_id": "tenant-456",
        "role": "admin",
    }

    response = await echo_agent.handle_request(request_data, request_id, auth_info)

    assert response["messages"][0]["role"] == "assistant"
    assert "Echo: Hello with auth!" in response["messages"][0]["content"]
    assert response["done"] is True
    assert response["threadId"] == "test-thread-789"
    assert response["metadata"]["authenticated"] is True
    assert response["metadata"]["auth"]["user_id"] == "user-123"
    assert response["metadata"]["auth"]["tenant_id"] == "tenant-456"
    assert response["metadata"]["auth"]["role"] == "admin"

@pytest.mark.asyncio
async def test_echo_agent_handle_streaming_request(echo_agent):
    """Test that the echo agent handles streaming requests."""
    request_data = {
        "messages": [
            {"role": "user", "content": "Hello, streaming!"}
        ],
        "threadId": "test-thread-stream",
    }
    request_id = "req-stream"

    # Collect all events from the streaming response
    events = []
    async for event in echo_agent.handle_streaming_request(request_data, request_id):
        events.append(event)

    # Verify we have at least a start event, some content events, and a done event
    assert len(events) >= 3

    # Parse the events
    parsed_events = []
    for event in events:
        assert event.startswith("data: ")
        event_data = json.loads(event.replace("data: ", "").strip())
        parsed_events.append(event_data)

    # Check for start event
    start_events = [e for e in parsed_events if e.get("type") == "start"]
    assert len(start_events) == 1
    assert "threadId" in start_events[0]

    # Check for content events
    content_events = [e for e in parsed_events if e.get("type") == "content"]
    assert len(content_events) > 0

    # Check for metadata event
    metadata_events = [e for e in parsed_events if e.get("type") == "metadata"]
    assert len(metadata_events) == 1
    assert "metadata" in metadata_events[0]
    assert metadata_events[0]["metadata"]["agent_name"] == "test_echo_agent"

    # Check for done event
    done_events = [e for e in parsed_events if e.get("type") == "done"]
    assert len(done_events) == 1
    assert "threadId" in done_events[0]
    assert "metadata" in done_events[0]
