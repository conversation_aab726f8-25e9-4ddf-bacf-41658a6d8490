"""
Tests for the agent configuration system.

This module implements a comprehensive testing strategy for the agent configuration system:
1. Unit tests for configuration models
2. Tests for configuration loading and caching
3. Tests for tenant-specific configurations
4. Validation tests
"""

import json
import os
import tempfile
from unittest.mock import patch

import pytest

from pi_lawyer.agents.config import (
    AgentConfig,
    LLMConfig,
    ToolConfig,
    get_agent_config,
    load_agent_configs_from_file,
    set_agent_config,
)


class TestAgentConfig:
    """Tests for the AgentConfig class."""
    
    def test_agent_config_creation(self):
        """Test creating an agent configuration."""
        config = AgentConfig(
            name="test_agent",
            agent_type="test",
            description="Test agent",
            llm_config=LLMConfig(
                provider="openai",
                model="gpt-4",
                temperature=0.7
            ),
            tools=[
                ToolConfig(name="test_tool", description="Test tool")
            ]
        )
        
        assert config.name == "test_agent"
        assert config.agent_type == "test"
        assert config.description == "Test agent"
        assert config.llm_config.provider == "openai"
        assert config.llm_config.model == "gpt-4"
        assert config.llm_config.temperature == 0.7
        assert len(config.tools) == 1
        assert config.tools[0].name == "test_tool"
        assert config.tools[0].description == "Test tool"
    
    def test_agent_config_validation(self):
        """Test agent configuration validation."""
        # Test temperature validation
        with pytest.raises(ValueError):
            LLMConfig(temperature=1.5)
        
        # Test tool name uniqueness
        with pytest.raises(ValueError):
            AgentConfig(
                name="test_agent",
                agent_type="test",
                tools=[
                    ToolConfig(name="test_tool"),
                    ToolConfig(name="test_tool")  # Duplicate name
                ]
            )
    
    def test_agent_config_to_dict(self):
        """Test converting an agent configuration to a dictionary."""
        config = AgentConfig(
            name="test_agent",
            agent_type="test",
            description="Test agent"
        )
        
        config_dict = config.to_dict()
        
        assert config_dict["name"] == "test_agent"
        assert config_dict["agent_type"] == "test"
        assert config_dict["description"] == "Test agent"
    
    def test_agent_config_from_dict(self):
        """Test creating an agent configuration from a dictionary."""
        config_dict = {
            "name": "test_agent",
            "agent_type": "test",
            "description": "Test agent",
            "llm_config": {
                "provider": "openai",
                "model": "gpt-4",
                "temperature": 0.7
            }
        }
        
        config = AgentConfig.from_dict(config_dict)
        
        assert config.name == "test_agent"
        assert config.agent_type == "test"
        assert config.description == "Test agent"
        assert config.llm_config.provider == "openai"
        assert config.llm_config.model == "gpt-4"
        assert config.llm_config.temperature == 0.7
    
    def test_agent_config_to_json(self):
        """Test converting an agent configuration to JSON."""
        config = AgentConfig(
            name="test_agent",
            agent_type="test",
            description="Test agent"
        )
        
        json_str = config.to_json()
        json_dict = json.loads(json_str)
        
        assert json_dict["name"] == "test_agent"
        assert json_dict["agent_type"] == "test"
        assert json_dict["description"] == "Test agent"
    
    def test_agent_config_from_json(self):
        """Test creating an agent configuration from JSON."""
        json_str = json.dumps({
            "name": "test_agent",
            "agent_type": "test",
            "description": "Test agent",
            "llm_config": {
                "provider": "openai",
                "model": "gpt-4",
                "temperature": 0.7
            }
        })
        
        config = AgentConfig.from_json(json_str)
        
        assert config.name == "test_agent"
        assert config.agent_type == "test"
        assert config.description == "Test agent"
        assert config.llm_config.provider == "openai"
        assert config.llm_config.model == "gpt-4"
        assert config.llm_config.temperature == 0.7
    
    def test_get_tool_config(self):
        """Test getting a tool configuration."""
        config = AgentConfig(
            name="test_agent",
            agent_type="test",
            tools=[
                ToolConfig(name="tool1", description="Tool 1"),
                ToolConfig(name="tool2", description="Tool 2")
            ]
        )
        
        tool1 = config.get_tool_config("tool1")
        tool2 = config.get_tool_config("tool2")
        tool3 = config.get_tool_config("tool3")
        
        assert tool1 is not None
        assert tool1.name == "tool1"
        assert tool1.description == "Tool 1"
        
        assert tool2 is not None
        assert tool2.name == "tool2"
        assert tool2.description == "Tool 2"
        
        assert tool3 is None
    
    def test_enable_disable_tool(self):
        """Test enabling and disabling a tool."""
        config = AgentConfig(
            name="test_agent",
            agent_type="test",
            tools=[
                ToolConfig(name="tool1", enabled=True),
                ToolConfig(name="tool2", enabled=False)
            ]
        )
        
        # Initial state
        assert config.get_tool_config("tool1").enabled is True
        assert config.get_tool_config("tool2").enabled is False
        
        # Disable tool1
        result1 = config.disable_tool("tool1")
        assert result1 is True
        assert config.get_tool_config("tool1").enabled is False
        
        # Enable tool2
        result2 = config.enable_tool("tool2")
        assert result2 is True
        assert config.get_tool_config("tool2").enabled is True
        
        # Try to enable/disable non-existent tool
        result3 = config.enable_tool("tool3")
        result4 = config.disable_tool("tool3")
        assert result3 is False
        assert result4 is False


class TestLLMConfig:
    """Tests for the LLMConfig class."""
    
    def test_llm_config_creation(self):
        """Test creating an LLM configuration."""
        config = LLMConfig(
            provider="openai",
            model="gpt-4",
            temperature=0.7,
            max_tokens=1000,
            top_p=0.9,
            frequency_penalty=0.5,
            presence_penalty=0.5,
            stop_sequences=["###"],
            timeout=30,
            api_key_env_var="CUSTOM_API_KEY",
            api_base_url="https://custom-api.example.com"
        )
        
        assert config.provider == "openai"
        assert config.model == "gpt-4"
        assert config.temperature == 0.7
        assert config.max_tokens == 1000
        assert config.top_p == 0.9
        assert config.frequency_penalty == 0.5
        assert config.presence_penalty == 0.5
        assert config.stop_sequences == ["###"]
        assert config.timeout == 30
        assert config.api_key_env_var == "CUSTOM_API_KEY"
        assert config.api_base_url == "https://custom-api.example.com"
    
    def test_api_key_from_env(self):
        """Test getting the API key from environment variables."""
        # Test with custom environment variable
        with patch.dict(os.environ, {"CUSTOM_API_KEY": "test-key-1"}):
            config1 = LLMConfig(api_key_env_var="CUSTOM_API_KEY")
            assert config1.api_key == "test-key-1"
        
        # Test with default environment variables
        with patch.dict(os.environ, {
            "OPENAI_API_KEY": "test-key-2",
            "ANTHROPIC_API_KEY": "test-key-3",
            "GOOGLE_API_KEY": "test-key-4",
            "AZURE_OPENAI_API_KEY": "test-key-5",
            "VOYAGE_API_KEY": "test-key-6"
        }):
            config2 = LLMConfig(provider="openai")
            assert config2.api_key == "test-key-2"
            
            config3 = LLMConfig(provider="anthropic")
            assert config3.api_key == "test-key-3"
            
            config4 = LLMConfig(provider="google")
            assert config4.api_key == "test-key-4"
            
            config5 = LLMConfig(provider="azure")
            assert config5.api_key == "test-key-5"
            
            config6 = LLMConfig(provider="voyage")
            assert config6.api_key == "test-key-6"


class TestConfigManagement:
    """Tests for configuration management functions."""
    
    def setup_method(self):
        """Set up the test."""
        # Clear the configuration cache
        from pi_lawyer.agents.config import _config_cache
        _config_cache.clear()
        
        # Clear the LRU cache
        get_agent_config.cache_clear()
    
    def test_get_agent_config_default(self):
        """Test getting a default agent configuration."""
        # Get a default configuration
        config = get_agent_config("research")
        
        assert config.name == "research_agent"
        assert config.agent_type == "research"
        
        # Get a configuration by agent type
        config = get_agent_config("intake")
        
        assert config.name == "intake_agent"
        assert config.agent_type == "intake"
    
    def test_get_agent_config_custom(self):
        """Test getting a custom agent configuration."""
        # Set a custom configuration
        custom_config = AgentConfig(
            name="custom_agent",
            agent_type="custom",
            description="Custom agent"
        )
        set_agent_config(custom_config)
        
        # Get the custom configuration
        config = get_agent_config("custom_agent")
        
        assert config.name == "custom_agent"
        assert config.agent_type == "custom"
        assert config.description == "Custom agent"
    
    def test_get_agent_config_tenant_specific(self):
        """Test getting a tenant-specific agent configuration."""
        # Set a global configuration
        global_config = AgentConfig(
            name="test_agent",
            agent_type="test",
            description="Global test agent"
        )
        set_agent_config(global_config)
        
        # Set a tenant-specific configuration
        tenant_config = AgentConfig(
            name="test_agent",
            agent_type="test",
            description="Tenant-specific test agent"
        )
        set_agent_config(tenant_config, tenant_id="tenant-123")
        
        # Get the global configuration
        config1 = get_agent_config("test_agent")
        
        assert config1.description == "Global test agent"
        
        # Get the tenant-specific configuration
        config2 = get_agent_config("test_agent", tenant_id="tenant-123")
        
        assert config2.description == "Tenant-specific test agent"
    
    def test_load_agent_configs_from_file(self):
        """Test loading agent configurations from a file."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
            json.dump({
                "test_agent": {
                    "name": "test_agent",
                    "agent_type": "test",
                    "description": "Test agent from file"
                }
            }, f)
            file_path = f.name
        
        try:
            # Load the configurations
            configs = load_agent_configs_from_file(file_path)
            
            assert len(configs) == 1
            assert "test_agent" in configs
            assert configs["test_agent"].description == "Test agent from file"
            
            # Get the configuration
            config = get_agent_config("test_agent")
            
            assert config.description == "Test agent from file"
        finally:
            # Clean up
            os.unlink(file_path)
    
    def test_load_agent_configs_from_file_tenant_specific(self):
        """Test loading tenant-specific agent configurations from a file."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
            json.dump({
                "test_agent": {
                    "name": "test_agent",
                    "agent_type": "test",
                    "description": "Tenant-specific test agent from file"
                }
            }, f)
            file_path = f.name
        
        try:
            # Load the configurations for a specific tenant
            configs = load_agent_configs_from_file(file_path, tenant_id="tenant-123")
            
            assert len(configs) == 1
            assert "test_agent" in configs
            assert configs["test_agent"].description == "Tenant-specific test agent from file"
            
            # Get the configuration for the tenant
            config = get_agent_config("test_agent", tenant_id="tenant-123")
            
            assert config.description == "Tenant-specific test agent from file"
        finally:
            # Clean up
            os.unlink(file_path)
