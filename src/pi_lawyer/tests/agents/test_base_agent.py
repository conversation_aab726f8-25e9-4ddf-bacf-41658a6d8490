"""
Tests for the BaseAgent class.

This module implements a comprehensive testing strategy for the BaseAgent class:
1. Unit tests for lifecycle hooks
2. Integration tests with state persistence
3. Tool registration and execution tests
4. Tenant isolation tests
"""

from typing import Any, Dict
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from langgraph.graph import StateGraph

from pi_lawyer.agents.base_agent import (
    AgentCleanupError,
    AgentExecutionError,
    AgentInitializationError,
    BaseAgent,
)
from pi_lawyer.agents.config import AgentConfig, LLMConfig, ToolConfig


# Test implementation of BaseAgent
class TestAgent(BaseAgent):
    """Test agent implementation."""

    async def initialize(self, state: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Initialize the agent."""
        # Add a system message
        if "messages" in state:
            state["messages"].append({
                "type": "system",
                "content": "You are a test agent."
            })

        # Add to memory
        if "memory" in state:
            state["memory"]["initialized"] = True
            state["memory"]["agent_name"] = self.name

        return state

    async def execute(self, state: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the agent."""
        # Add an AI message
        if "messages" in state:
            state["messages"].append({
                "type": "ai",
                "content": "This is a test response."
            })

        # Add to memory
        if "memory" in state:
            state["memory"]["executed"] = True

        return state

    async def cleanup(self, state: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Clean up the agent."""
        # Add to memory
        if "memory" in state:
            state["memory"]["cleaned_up"] = True

        return state


# Test implementation that raises errors
class ErrorAgent(BaseAgent):
    """Test agent that raises errors."""

    async def initialize(self, state: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Initialize the agent."""
        raise ValueError("Initialization error")

    async def execute(self, state: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the agent."""
        raise ValueError("Execution error")

    async def cleanup(self, state: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Clean up the agent."""
        raise ValueError("Cleanup error")


@pytest.mark.asyncio
class TestBaseAgent:
    """Tests for the BaseAgent class."""

    @pytest.fixture
    def agent_config(self):
        """Create a test agent configuration."""
        return AgentConfig(
            name="test_agent",
            agent_type="test",
            description="Test agent",
            llm_config=LLMConfig(
                provider="openai",
                model="gpt-4",
                temperature=0.7
            ),
            tools=[
                ToolConfig(name="test_tool", description="Test tool")
            ]
        )

    @pytest.fixture
    def mock_tool_executor(self):
        """Mock the tool executor."""
        with patch("pi_lawyer.agents.base_agent.get_tool_executor") as mock:
            instance = MagicMock()
            mock.return_value = instance
            instance.execute_tool = AsyncMock()
            instance.tools = {"test_tool": lambda **kwargs: {"result": "test_result"}}
            yield instance

    @pytest.fixture
    def mock_state_manager(self):
        """Mock the StateManager."""
        with patch("pi_lawyer.agents.base_agent.StateManager") as mock:
            instance = MagicMock()
            mock.return_value = instance
            instance.load_state = AsyncMock()
            instance.save_state = AsyncMock()
            yield instance

    @pytest.fixture
    def test_agent(self, agent_config, mock_tool_executor):
        """Create a test agent."""
        # Register the test tool in the agent
        agent = TestAgent(agent_config)
        agent.register_tool("test_tool", lambda **kwargs: {"result": "test_result"})
        return agent

    @pytest.fixture
    def error_agent(self, agent_config, mock_tool_executor):
        """Create an error agent."""
        # Register the test tool in the agent
        agent = ErrorAgent(agent_config)
        agent.register_tool("test_tool", lambda **kwargs: {"result": "test_result"})
        return agent

    async def test_agent_initialization(self, test_agent):
        """Test agent initialization."""
        assert test_agent.name == "test_agent"
        assert test_agent.agent_type == "test"
        assert test_agent.description == "Test agent"
        assert test_agent.version == "1.0.0"
        assert len(test_agent.tools) == 1
        assert "test_tool" in test_agent.tools

    async def test_create_state(self, test_agent):
        """Test creating a state."""
        state = test_agent.create_state(
            tenant_id="tenant-123",
            user_id="user-456"
        )

        assert state.tenant_id == "tenant-123"
        assert state.user_id == "user-456"
        assert state.agent_type == "test"
        assert len(state.messages) == 0
        assert isinstance(state.memory, dict)

    async def test_create_graph(self, test_agent):
        """Test creating a graph."""
        graph = test_agent.create_graph()

        assert isinstance(graph, StateGraph)
        assert "initialize" in graph.nodes
        assert "execute" in graph.nodes
        assert "cleanup" in graph.nodes

    async def test_lifecycle_hooks(self, test_agent):
        """Test lifecycle hooks."""
        # Create a state
        state = {
            "messages": [],
            "memory": {},
            "tenant_id": "tenant-123",
            "user_id": "user-456",
            "thread_id": "thread-789",
            "agent_type": "test"
        }

        # Initialize
        state = await test_agent._initialize_node(state, {})
        assert len(state["messages"]) == 1
        assert state["messages"][0]["type"] == "system"
        assert state["memory"]["initialized"] is True

        # Execute
        state = await test_agent._execute_node(state, {})
        assert len(state["messages"]) == 2
        assert state["messages"][1]["type"] == "ai"
        assert state["memory"]["executed"] is True

        # Cleanup
        state = await test_agent._cleanup_node(state, {})
        assert state["memory"]["cleaned_up"] is True

    async def test_error_handling(self, error_agent):
        """Test error handling."""
        # Create a state
        state = {
            "messages": [],
            "memory": {},
            "tenant_id": "tenant-123",
            "user_id": "user-456",
            "thread_id": "thread-789",
            "agent_type": "test"
        }

        # Initialize
        with pytest.raises(AgentInitializationError):
            await error_agent._initialize_node(state, {})

        # Execute
        with pytest.raises(AgentExecutionError):
            await error_agent._execute_node(state, {})

        # Cleanup
        with pytest.raises(AgentCleanupError):
            await error_agent._cleanup_node(state, {})

    async def test_load_state(self, test_agent, mock_state_manager):
        """Test loading a state."""
        # Mock the state manager
        mock_state = MagicMock()
        mock_state_manager.load_state.return_value = mock_state

        # Load the state
        state = await test_agent.load_state("thread-123", "tenant-456")

        # Verify
        assert state == mock_state
        mock_state_manager.load_state.assert_called_once_with("thread-123")

    async def test_save_state(self, test_agent, mock_state_manager):
        """Test saving a state."""
        # Mock the state manager
        mock_state = MagicMock()
        mock_state.tenant_id = "tenant-456"
        mock_state_manager.save_state.return_value = "record-123"

        # Save the state
        record_id = await test_agent.save_state(mock_state)

        # Verify
        assert record_id == "record-123"
        mock_state_manager.save_state.assert_called_once_with(mock_state)

    async def test_execute_tool(self, test_agent, mock_tool_executor):
        """Test executing a tool."""
        # Mock the tool executor
        mock_tool_executor.execute_tool.return_value = {"result": "success"}

        # Execute the tool
        result = await test_agent.execute_tool(
            tool_name="test_tool",
            tool_args={"arg1": "value1"},
            tenant_id="tenant-123"
        )

        # Verify
        assert result == {"result": "success"}
        mock_tool_executor.execute_tool.assert_called_once_with(
            "test_tool",
            {"arg1": "value1", "tenant_id": "tenant-123"}
        )

    async def test_invoke(self, test_agent):
        """Test invoking the agent."""
        # Mock the graph
        mock_graph = MagicMock()
        mock_compiled_graph = MagicMock()
        mock_graph.compile.return_value = mock_compiled_graph
        mock_compiled_graph.ainvoke = AsyncMock(return_value={"result": "success"})

        # Mock create_graph
        with patch.object(test_agent, "create_graph", return_value=mock_graph):
            # Invoke the agent
            result = await test_agent.invoke(
                {"messages": [{"type": "human", "content": "Hello"}]},
                {"configurable": {"tenant_id": "tenant-123", "thread_id": "thread-456"}}
            )

            # Verify
            assert result == {"result": "success"}
            mock_graph.compile.assert_called_once()
            mock_compiled_graph.ainvoke.assert_called_once()

    async def test_tenant_isolation(self, test_agent, mock_tool_executor):
        """Test tenant isolation."""
        # Mock the tool executor
        mock_tool_executor.execute_tool.return_value = {"result": "success"}

        # Execute the tool for tenant 1
        await test_agent.execute_tool(
            tool_name="test_tool",
            tool_args={"arg1": "value1"},
            tenant_id="tenant-1"
        )

        # Execute the tool for tenant 2
        await test_agent.execute_tool(
            tool_name="test_tool",
            tool_args={"arg1": "value1"},
            tenant_id="tenant-2"
        )

        # Verify
        assert mock_tool_executor.execute_tool.call_count == 2
        mock_tool_executor.execute_tool.assert_any_call(
            "test_tool",
            {"arg1": "value1", "tenant_id": "tenant-1"}
        )
        mock_tool_executor.execute_tool.assert_any_call(
            "test_tool",
            {"arg1": "value1", "tenant_id": "tenant-2"}
        )

    async def test_invoke_without_tenant_id(self, test_agent):
        """Test invoking the agent without a tenant ID."""
        # Mock the graph
        mock_graph = MagicMock()
        mock_compiled_graph = MagicMock()
        mock_graph.compile.return_value = mock_compiled_graph

        # Mock create_graph
        with patch.object(test_agent, "create_graph", return_value=mock_graph):
            # Invoke the agent without a tenant ID
            with pytest.raises(AgentExecutionError):
                await test_agent.invoke(
                    {"messages": [{"type": "human", "content": "Hello"}]},
                    {"configurable": {"thread_id": "thread-456"}}
                )
