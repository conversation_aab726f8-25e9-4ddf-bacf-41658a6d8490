from unittest import mock

import pytest

from pi_lawyer.utils.gemini_client import Gemini<PERSON>lient


@pytest.mark.asyncio
async def test_gemini_client_init(monkeypatch):
    monkeypatch.setenv("GEMINI_API_KEY", "fake-key")
    client = GeminiClient()
    assert client is not None


@pytest.mark.asyncio
async def test_generate_legal_queries(monkeypatch):
    monkeypatch.setenv("GEMINI_API_KEY", "fake-key")
    client = GeminiClient()
    with mock.patch.object(client, "generate_legal_queries", return_value=["q1", "q2"]):
        result = await client.generate_legal_queries("test", num_queries=2)
        assert isinstance(result, list)


# TODO: Add real API mock and test synthesize_research.
