from unittest import mock

from pi_lawyer.utils.voyage_reranker import VoyageAIReranker


def test_reranker_init(monkeypatch):
    monkeypatch.setenv("VOYAGE_API_KEY", "fake-key")
    reranker = VoyageAIReranker()
    assert reranker is not None


@mock.patch("pi_lawyer.utils.voyage_reranker.VoyageAIReranker.rerank_texts")
def test_rerank_texts_mocked(mock_rerank):
    reranker = VoyageAIReranker()
    mock_rerank.return_value = ["doc1", "doc2"]
    result = reranker.rerank_texts("query", ["doc1", "doc2"])
    assert isinstance(result, list)


# TODO: Add more coverage for rerank_langchain_documents.
# TODO: Add integration/external API tests.

# Add more targeted tests when function signatures are known
