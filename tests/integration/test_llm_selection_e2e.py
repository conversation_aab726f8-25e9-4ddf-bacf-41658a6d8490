"""
End-to-end integration tests for the LLM selection system.
"""


import pytest


class TestLLMSelectionIntegration:
    """Integration tests for the complete LLM selection workflow."""

    @pytest.fixture
    def mock_database_with_data(self):
        """Mock database with sample LLM selection data."""
        return {
            'admin_llm_selection': [
                {
                    'id': 'sel-1',
                    'tenant_id': '*',
                    'agent': 'researchAgent',
                    'node': 'query_gen',
                    'model_name': 'groq/voyage-large-turbo',
                    'temperature': 0.2
                },
                {
                    'id': 'sel-2',
                    'tenant_id': '*',
                    'agent': 'caseCrudAgent',
                    'node': None,
                    'model_name': 'gemini/gemini-2.0-flash',
                    'temperature': 0.2
                },
                {
                    'id': 'sel-3',
                    'tenant_id': 'tenant1',
                    'agent': 'researchAgent',
                    'node': None,
                    'model_name': 'anthropic/claude-3-sonnet',
                    'temperature': 0.3
                }
            ],
            'llm_models': [
                {
                    'id': 'gpt-4o',
                    'provider': 'openai',
                    'name': 'GPT-4o',
                    'description': 'Most advanced GPT-4 model',
                    'max_tokens': 128000
                },
                {
                    'id': 'claude-3-sonnet',
                    'provider': 'anthropic',
                    'name': 'Claude 3 Sonnet',
                    'description': 'Balanced Claude model',
                    'max_tokens': 200000
                },
                {
                    'id': 'gemini-2.0-flash',
                    'provider': 'gemini',
                    'name': 'Gemini 2.0 Flash',
                    'description': 'Latest Gemini model',
                    'max_tokens': 1000000
                }
            ]
        }

    @pytest.mark.asyncio
    async def test_full_workflow_create_selection(self, mock_database_with_data):
        """Test the complete workflow of creating a new LLM selection."""
        # Step 1: User loads the UI and sees current selections
        current_selections = mock_database_with_data['admin_llm_selection']
        available_models = mock_database_with_data['llm_models']
        
        assert len(current_selections) == 3
        assert len(available_models) == 3

        # Step 2: User creates a new selection
        new_selection = {
            'tenant_id': '*',
            'agent': 'taskCrudAgent',
            'node': None,
            'model_name': 'openai/gpt-4o',
            'temperature': 0.2
        }

        # Step 3: Validate the new selection
        assert new_selection['tenant_id'] in ['*', 'tenant1', 'tenant2']
        assert new_selection['agent'] in [
            'researchAgent', 'intakeAgent', 'supervisorAgent',
            'calendarCrudAgent', 'taskCrudAgent', 'caseCrudAgent'
        ]
        assert new_selection['model_name'].count('/') == 1  # provider/model format
        assert 0.0 <= new_selection['temperature'] <= 2.0

        # Step 4: Simulate database insertion
        mock_database_with_data['admin_llm_selection'].append({
            'id': 'sel-4',
            **new_selection
        })

        # Step 5: Verify the selection was added
        updated_selections = mock_database_with_data['admin_llm_selection']
        assert len(updated_selections) == 4
        
        new_record = next(
            (sel for sel in updated_selections if sel['agent'] == 'taskCrudAgent'),
            None
        )
        assert new_record is not None
        assert new_record['model_name'] == 'openai/gpt-4o'

    @pytest.mark.asyncio
    async def test_inheritance_resolution_workflow(self, mock_database_with_data):
        """Test the complete inheritance resolution workflow."""
        selections = mock_database_with_data['admin_llm_selection']
        
        # Test case: caseCrudAgent with router node
        agent_name = 'caseCrudAgent'
        node_name = 'router'
        tenant_id = '*'

        # Step 1: Look for node-specific configuration
        node_selection = next(
            (sel for sel in selections 
             if sel['tenant_id'] == tenant_id and 
                sel['agent'] == agent_name and 
                sel['node'] == node_name),
            None
        )

        # Step 2: If no node config, look for agent-level configuration
        if not node_selection:
            agent_selection = next(
                (sel for sel in selections 
                 if sel['tenant_id'] == tenant_id and 
                    sel['agent'] == agent_name and 
                    sel['node'] is None),
                None
            )
        else:
            agent_selection = None

        # Step 3: If no agent config, use global default
        if not node_selection and not agent_selection:
            default_model = 'openai/gpt-3.5-turbo'
        else:
            default_model = None

        # Step 4: Resolve the final model
        if node_selection:
            resolved_model = node_selection['model_name']
            resolved_temp = node_selection['temperature']
            inheritance_source = 'node'
        elif agent_selection:
            resolved_model = agent_selection['model_name']
            resolved_temp = agent_selection['temperature']
            inheritance_source = 'agent'
        else:
            resolved_model = default_model
            resolved_temp = 0.2
            inheritance_source = 'default'

        # Verify inheritance resolution
        assert resolved_model == 'gemini/gemini-2.0-flash'  # From agent config
        assert resolved_temp == 0.2
        assert inheritance_source == 'agent'

    @pytest.mark.asyncio
    async def test_tenant_isolation_workflow(self, mock_database_with_data):
        """Test tenant isolation in the complete workflow."""
        selections = mock_database_with_data['admin_llm_selection']

        # Test global tenant selections
        global_selections = [
            sel for sel in selections if sel['tenant_id'] == '*'
        ]
        assert len(global_selections) == 2

        # Test tenant-specific selections
        tenant1_selections = [
            sel for sel in selections if sel['tenant_id'] == 'tenant1'
        ]
        assert len(tenant1_selections) == 1

        # Test that tenant1 sees both global and tenant-specific
        tenant1_effective_selections = [
            sel for sel in selections 
            if sel['tenant_id'] in ['*', 'tenant1']
        ]
        assert len(tenant1_effective_selections) == 3

        # Test precedence: tenant-specific overrides global
        research_agent_global = next(
            (sel for sel in global_selections 
             if sel['agent'] == 'researchAgent' and sel['node'] == 'query_gen'),
            None
        )
        research_agent_tenant1 = next(
            (sel for sel in tenant1_selections 
             if sel['agent'] == 'researchAgent'),
            None
        )

        assert research_agent_global is not None
        assert research_agent_tenant1 is not None
        assert research_agent_global['model_name'] != research_agent_tenant1['model_name']

    @pytest.mark.asyncio
    async def test_ui_to_backend_integration(self):
        """Test the integration between UI components and backend API."""
        # Simulate UI component state
        ui_state = {
            'selectedTenant': '*',
            'searchQuery': '',
            'selectedProvider': '',
            'agents': [],
            'isLoading': False,
            'error': None
        }

        # Simulate API call from UI
        api_request = {
            'method': 'GET',
            'url': '/api/admin/llm-selections',
            'params': {
                'tenant': ui_state['selectedTenant'],
                'search': ui_state['searchQuery'],
                'provider': ui_state['selectedProvider']
            }
        }

        # Simulate API response
        api_response = {
            'agents': [
                {
                    'name': 'researchAgent',
                    'displayName': 'Research Agent',
                    'icon': '🔍',
                    'currentModel': None,
                    'isConfigured': False,
                    'nodes': [
                        {
                            'name': 'query_gen',
                            'displayName': 'Query Generation',
                            'currentModel': 'groq/voyage-large-turbo',
                            'isConfigured': True
                        }
                    ]
                }
            ],
            'selections': [
                {
                    'id': 'sel-1',
                    'tenant_id': '*',
                    'agent': 'researchAgent',
                    'node': 'query_gen',
                    'model_name': 'groq/voyage-large-turbo',
                    'temperature': 0.2
                }
            ]
        }

        # Verify API request structure
        assert api_request['method'] == 'GET'
        assert 'tenant' in api_request['params']
        assert 'search' in api_request['params']
        assert 'provider' in api_request['params']

        # Verify API response structure
        assert 'agents' in api_response
        assert 'selections' in api_response
        assert len(api_response['agents']) > 0
        assert len(api_response['selections']) > 0

        # Verify agent structure
        agent = api_response['agents'][0]
        assert 'name' in agent
        assert 'displayName' in agent
        assert 'icon' in agent
        assert 'nodes' in agent
        assert 'isConfigured' in agent

        # Verify node structure
        node = agent['nodes'][0]
        assert 'name' in node
        assert 'displayName' in node
        assert 'currentModel' in node
        assert 'isConfigured' in node

    @pytest.mark.asyncio
    async def test_error_handling_workflow(self):
        """Test error handling throughout the workflow."""
        error_scenarios = [
            {
                'scenario': 'Database connection error',
                'error_type': 'ConnectionError',
                'expected_status': 500,
                'expected_message': 'Database connection failed'
            },
            {
                'scenario': 'Invalid agent name',
                'error_type': 'ValidationError',
                'expected_status': 400,
                'expected_message': 'Unknown agent: invalidAgent'
            },
            {
                'scenario': 'Unauthorized access',
                'error_type': 'AuthenticationError',
                'expected_status': 401,
                'expected_message': 'Unauthorized'
            },
            {
                'scenario': 'Insufficient permissions',
                'error_type': 'AuthorizationError',
                'expected_status': 403,
                'expected_message': 'Forbidden'
            }
        ]

        for scenario in error_scenarios:
            # Simulate error condition
            error_response = {
                'status': scenario['expected_status'],
                'error': scenario['expected_message']
            }

            # Verify error response structure
            assert 'status' in error_response
            assert 'error' in error_response
            assert error_response['status'] >= 400
            assert len(error_response['error']) > 0

    @pytest.mark.asyncio
    async def test_security_audit_logging(self):
        """Test security audit logging integration."""
        # Simulate a model selection change
        change_event = {
            'event_type': 'llm_selection.updated',
            'tenant_id': '*',
            'agent': 'caseCrudAgent',
            'node': 'router',
            'old_model': 'gemini/gemini-2.0-flash',
            'new_model': 'anthropic/claude-3-sonnet',
            'user_id': 'test-user-id',
            'user_email': '<EMAIL>',
            'timestamp': '2024-01-01T00:00:00Z',
            'ip_address': '***********'
        }

        # Verify audit log structure
        required_fields = [
            'event_type', 'tenant_id', 'agent', 'user_id', 
            'user_email', 'timestamp'
        ]

        for field in required_fields:
            assert field in change_event
            assert change_event[field] is not None

        # Verify event type format
        assert change_event['event_type'].startswith('llm_selection.')
        assert change_event['event_type'] in [
            'llm_selection.created',
            'llm_selection.updated', 
            'llm_selection.deleted'
        ]

    @pytest.mark.asyncio
    async def test_performance_with_large_dataset(self):
        """Test performance with a large number of selections."""
        # Simulate large dataset
        large_dataset = []
        
        agents = ['researchAgent', 'caseCrudAgent', 'taskCrudAgent', 'calendarCrudAgent']
        nodes = ['router', 'create', 'read', 'update', 'delete']
        tenants = ['*'] + [f'tenant{i}' for i in range(1, 101)]  # 100 tenants
        
        selection_id = 1
        for tenant in tenants:
            for agent in agents:
                # Agent-level selection
                large_dataset.append({
                    'id': f'sel-{selection_id}',
                    'tenant_id': tenant,
                    'agent': agent,
                    'node': None,
                    'model_name': 'openai/gpt-3.5-turbo',
                    'temperature': 0.2
                })
                selection_id += 1
                
                # Node-level selections
                for node in nodes:
                    large_dataset.append({
                        'id': f'sel-{selection_id}',
                        'tenant_id': tenant,
                        'agent': agent,
                        'node': node,
                        'model_name': 'openai/gpt-4o',
                        'temperature': 0.2
                    })
                    selection_id += 1

        # Verify dataset size
        expected_size = len(tenants) * len(agents) * (1 + len(nodes))  # Agent + nodes
        assert len(large_dataset) == expected_size

        # Test filtering performance (simulated)
        tenant_filter = 'tenant50'
        filtered_data = [
            sel for sel in large_dataset 
            if sel['tenant_id'] == tenant_filter
        ]
        
        expected_filtered_size = len(agents) * (1 + len(nodes))
        assert len(filtered_data) == expected_filtered_size

        # Test search performance (simulated)
        search_query = 'research'
        search_results = [
            sel for sel in large_dataset
            if search_query.lower() in sel['agent'].lower()
        ]
        
        expected_search_results = len(tenants) * (1 + len(nodes))  # researchAgent entries
        assert len(search_results) == expected_search_results


if __name__ == '__main__':
    pytest.main([__file__])
