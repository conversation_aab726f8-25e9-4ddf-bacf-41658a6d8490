"""
Tests for subscription API endpoints.

This module contains comprehensive tests for the subscription API,
including happy path, edge cases, and error conditions.
"""

from unittest.mock import AsyncMock, patch
from uuid import uuid4

import pytest
from fastapi import FastAPI, status
from fastapi.testclient import TestClient

# Import the subscription API directly to avoid complex dependencies
from backend.api.subscription_api import router as subscription_router
from tests.factories import (
    create_subscription_addon,
    create_subscription_plan,
)


@pytest.fixture
def app():
    """Create FastAPI app for testing."""
    app = FastAPI()
    app.include_router(subscription_router)
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_db_session():
    """Mock database session."""
    return AsyncMock()


@pytest.fixture
def sample_tenant_id():
    """Sample tenant ID for testing."""
    return uuid4()


@pytest.fixture
def sample_plan():
    """Sample subscription plan for testing."""
    return create_subscription_plan(
        name="Professional Plan",
        code="professional",
        features={
            "feature_ai_research": True,
            "feature_case_management": True,
            "feature_calendar_booking": True,
            "feature_document_upload": True,
        }
    )


@pytest.fixture
def sample_addon():
    """Sample subscription addon for testing."""
    return create_subscription_addon(
        name="Voice Intake",
        code="voice_intake",
        features={
            "feature_voice_intake": True,
        }
    )


class TestGetTenantFeatures:
    """Test cases for the GET /tenant/{tenant_id}/features endpoint."""

    @patch("backend.api.subscription_api.get_tenant_features")
    async def test_get_features_success(
        self,
        mock_get_tenant_features,
        app,
        sample_tenant_id,
    ):
        """Test successful retrieval of tenant features."""
        # Override dependencies
        from backend.api.dependencies.auth import get_current_tenant
        from backend.db.session import get_db_session

        mock_db_session = AsyncMock()
        app.dependency_overrides[get_current_tenant] = lambda: sample_tenant_id
        app.dependency_overrides[get_db_session] = lambda: mock_db_session

        # Mock the get_tenant_features function to return expected features
        mock_get_tenant_features.return_value = {
            "ai_research", "case_management", "calendar_booking", "document_upload"
        }

        client = TestClient(app)

        # Make request
        response = client.get(
            f"/tenant/{sample_tenant_id}/features",
            headers={
                "Authorization": "Bearer test-token",
                "X-Tenant-ID": str(sample_tenant_id),
            }
        )

        # Clean up dependency overrides
        app.dependency_overrides.clear()
        
        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["tenant_id"] == str(sample_tenant_id)
        assert "features" in data
        assert isinstance(data["features"], list)
        
        # Check that features from the plan are included
        features = data["features"]
        assert "ai_research" in features
        assert "case_management" in features
        assert "calendar_booking" in features
        assert "document_upload" in features

    @patch("backend.api.subscription_api.get_tenant_features")
    async def test_get_features_with_addons(
        self,
        mock_get_tenant_features,
        app,
        sample_tenant_id,
    ):
        """Test retrieval of tenant features including addons."""
        # Override dependencies
        from backend.api.dependencies.auth import get_current_tenant
        from backend.db.session import get_db_session

        mock_db_session = AsyncMock()
        app.dependency_overrides[get_current_tenant] = lambda: sample_tenant_id
        app.dependency_overrides[get_db_session] = lambda: mock_db_session

        # Mock the get_tenant_features function to return features from both plan and addon
        mock_get_tenant_features.return_value = {
            "ai_research", "case_management", "calendar_booking", "document_upload", "voice_intake"
        }

        client = TestClient(app)

        # Make request
        response = client.get(
            f"/tenant/{sample_tenant_id}/features",
            headers={
                "Authorization": "Bearer test-token",
                "X-Tenant-ID": str(sample_tenant_id),
            }
        )

        # Clean up dependency overrides
        app.dependency_overrides.clear()
        
        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        features = data["features"]
        
        # Check that features from both plan and addon are included
        assert "ai_research" in features  # From plan
        assert "voice_intake" in features  # From addon

    @patch("backend.api.subscription_api.get_tenant_features")
    async def test_get_features_no_subscription(
        self,
        mock_get_tenant_features,
        app,
        sample_tenant_id,
    ):
        """Test error when tenant has no active subscription."""
        # Override dependencies
        from fastapi import HTTPException

        from backend.api.dependencies.auth import get_current_tenant
        from backend.db.session import get_db_session

        mock_db_session = AsyncMock()
        app.dependency_overrides[get_current_tenant] = lambda: sample_tenant_id
        app.dependency_overrides[get_db_session] = lambda: mock_db_session

        # Mock the get_tenant_features function to raise 404 for no subscription
        mock_get_tenant_features.side_effect = HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No active subscription found for tenant"
        )

        client = TestClient(app)

        # Make request
        response = client.get(
            f"/tenant/{sample_tenant_id}/features",
            headers={
                "Authorization": "Bearer test-token",
                "X-Tenant-ID": str(sample_tenant_id),
            }
        )

        # Clean up dependency overrides
        app.dependency_overrides.clear()

        # Assertions
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "No active subscription found" in data["detail"]

    @patch("backend.api.subscription_api.get_current_tenant")
    async def test_get_features_tenant_mismatch(
        self,
        mock_get_current_tenant,
        client,
        sample_tenant_id,
    ):
        """Test error when requested tenant doesn't match authenticated tenant."""
        # Setup mocks - different tenant IDs
        different_tenant_id = uuid4()
        mock_get_current_tenant.return_value = different_tenant_id
        
        # Make request
        response = client.get(
            f"/tenant/{sample_tenant_id}/features",
            headers={
                "Authorization": "Bearer test-token",
                "X-Tenant-ID": str(different_tenant_id),
            }
        )
        
        # Assertions
        assert response.status_code == status.HTTP_403_FORBIDDEN
        data = response.json()
        assert "Access denied" in data["detail"]

    async def test_get_features_missing_auth_header(self, client, sample_tenant_id):
        """Test error when authorization header is missing."""
        response = client.get(f"/tenant/{sample_tenant_id}/features")

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    async def test_get_features_missing_tenant_header(self, client, sample_tenant_id):
        """Test error when X-Tenant-ID header is missing."""
        response = client.get(
            f"/tenant/{sample_tenant_id}/features",
            headers={"Authorization": "Bearer test-token"}
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    async def test_get_features_invalid_tenant_id(self, client):
        """Test error when tenant ID is not a valid UUID."""
        response = client.get(
            "/tenant/invalid-uuid/features",
            headers={
                "Authorization": "Bearer test-token",
                "X-Tenant-ID": "invalid-uuid",
            }
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @patch("backend.api.subscription_api.get_tenant_features")
    async def test_get_features_inactive_plan(
        self,
        mock_get_tenant_features,
        app,
        sample_tenant_id,
    ):
        """Test error when subscription plan is inactive."""
        # Override dependencies
        from fastapi import HTTPException

        from backend.api.dependencies.auth import get_current_tenant
        from backend.db.session import get_db_session

        mock_db_session = AsyncMock()
        app.dependency_overrides[get_current_tenant] = lambda: sample_tenant_id
        app.dependency_overrides[get_db_session] = lambda: mock_db_session

        # Mock the get_tenant_features function to raise 404 for inactive plan
        mock_get_tenant_features.side_effect = HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription plan not found or inactive"
        )

        client = TestClient(app)

        # Make request
        response = client.get(
            f"/tenant/{sample_tenant_id}/features",
            headers={
                "Authorization": "Bearer test-token",
                "X-Tenant-ID": str(sample_tenant_id),
            }
        )

        # Clean up dependency overrides
        app.dependency_overrides.clear()

        # Assertions
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "Subscription plan not found" in data["detail"]


class TestGetTenantFeaturesIntegration:
    """Integration tests for tenant features functionality."""

    @patch("backend.api.subscription_api.get_tenant_features")
    async def test_fallback_features_for_basic_plan(
        self,
        mock_get_tenant_features,
        app,
        sample_tenant_id,
    ):
        """Test fallback feature assignment for basic plans."""
        # Override dependencies
        from backend.api.dependencies.auth import get_current_tenant
        from backend.db.session import get_db_session

        mock_db_session = AsyncMock()
        app.dependency_overrides[get_current_tenant] = lambda: sample_tenant_id
        app.dependency_overrides[get_db_session] = lambda: mock_db_session

        # Mock the get_tenant_features function to return basic plan fallback features
        mock_get_tenant_features.return_value = {
            "document_upload", "basic_search"
        }

        client = TestClient(app)

        # Make request
        response = client.get(
            f"/tenant/{sample_tenant_id}/features",
            headers={
                "Authorization": "Bearer test-token",
                "X-Tenant-ID": str(sample_tenant_id),
            }
        )

        # Clean up dependency overrides
        app.dependency_overrides.clear()

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        features = data["features"]

        # Should have fallback features for basic plan
        assert "document_upload" in features
        assert "basic_search" in features

    @patch("backend.api.subscription_api.get_tenant_features")
    async def test_enterprise_plan_features(
        self,
        mock_get_tenant_features,
        app,
        sample_tenant_id,
    ):
        """Test feature assignment for enterprise plans."""
        # Override dependencies
        from backend.api.dependencies.auth import get_current_tenant
        from backend.db.session import get_db_session

        mock_db_session = AsyncMock()
        app.dependency_overrides[get_current_tenant] = lambda: sample_tenant_id
        app.dependency_overrides[get_db_session] = lambda: mock_db_session

        # Mock the get_tenant_features function to return enterprise plan features
        mock_get_tenant_features.return_value = {
            "document_upload", "basic_search", "ai_research",
            "case_management", "calendar_booking", "voice_intake",
            "advanced_analytics", "custom_integrations"
        }

        client = TestClient(app)

        # Make request
        response = client.get(
            f"/tenant/{sample_tenant_id}/features",
            headers={
                "Authorization": "Bearer test-token",
                "X-Tenant-ID": str(sample_tenant_id),
            }
        )

        # Clean up dependency overrides
        app.dependency_overrides.clear()

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        features = data["features"]

        # Should have enterprise features
        assert "voice_intake" in features
        assert "advanced_analytics" in features
        assert "custom_integrations" in features

    @patch("backend.api.subscription_api.get_tenant_features")
    async def test_features_list_format_in_plan(
        self,
        mock_get_tenant_features,
        app,
        sample_tenant_id,
    ):
        """Test handling of features stored as a list in plan features."""
        # Override dependencies
        from backend.api.dependencies.auth import get_current_tenant
        from backend.db.session import get_db_session

        mock_db_session = AsyncMock()
        app.dependency_overrides[get_current_tenant] = lambda: sample_tenant_id
        app.dependency_overrides[get_db_session] = lambda: mock_db_session

        # Mock the get_tenant_features function to return custom features from list
        mock_get_tenant_features.return_value = {
            "custom_feature_1", "custom_feature_2", "custom_feature_3"
        }

        client = TestClient(app)

        # Make request
        response = client.get(
            f"/tenant/{sample_tenant_id}/features",
            headers={
                "Authorization": "Bearer test-token",
                "X-Tenant-ID": str(sample_tenant_id),
            }
        )

        # Clean up dependency overrides
        app.dependency_overrides.clear()

        # Assertions
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        features = data["features"]

        # Should include features from the list
        assert "custom_feature_1" in features
        assert "custom_feature_2" in features
        assert "custom_feature_3" in features

    @patch("backend.api.subscription_api.get_tenant_features")
    async def test_database_error_handling(
        self,
        mock_get_tenant_features,
        app,
        sample_tenant_id,
    ):
        """Test handling of database errors."""
        # Override dependencies
        from fastapi import HTTPException

        from backend.api.dependencies.auth import get_current_tenant
        from backend.db.session import get_db_session

        mock_db_session = AsyncMock()
        app.dependency_overrides[get_current_tenant] = lambda: sample_tenant_id
        app.dependency_overrides[get_db_session] = lambda: mock_db_session

        # Mock the get_tenant_features function to raise a database error
        mock_get_tenant_features.side_effect = HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

        client = TestClient(app)

        # Make request
        response = client.get(
            f"/tenant/{sample_tenant_id}/features",
            headers={
                "Authorization": "Bearer test-token",
                "X-Tenant-ID": str(sample_tenant_id),
            }
        )

        # Clean up dependency overrides
        app.dependency_overrides.clear()

        # Assertions
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        data = response.json()
        assert "Internal server error" in data["detail"]



