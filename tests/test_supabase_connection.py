import logging
import os

from dotenv import load_dotenv

from supabase import Client, create_client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()


def main():
    # Get Supabase credentials
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")

    logger.info(f"Testing connection to Supabase at {supabase_url}")

    try:
        # Initialize client
        supabase: Client = create_client(supabase_url, supabase_key)

        # Test 1: Try to get user profile (this should work with anon key)
        logger.info("Test 1: Checking authentication...")
        auth_response = supabase.auth.get_user(os.getenv("SUPABASE_ACCESS_TOKEN"))
        logger.info("✓ Authentication working")

        # Test 2: Try to read from public schema
        logger.info("\nTest 2: Checking public schema access...")
        docs = supabase.from_("documents").select("*").limit(1).execute()
        logger.info(
            f"✓ Can query public.documents table. Found {len(docs.data)} records"
        )

        # Test 3: Try to read from tenants schema
        logger.info("\nTest 3: Checking tenants schema access...")
        cases = supabase.from_("cases").select("*").limit(1).execute()
        logger.info(f"✓ Can query tenants.cases table. Found {len(cases.data)} records")

        logger.info("\n✓ All connection tests passed!")

    except Exception as e:
        logger.error(f"\n✗ Connection test failed: {str(e)}")
        if hasattr(e, "response"):
            logger.error(
                f"Response details: {e.response.text if hasattr(e, 'response') else 'No response text'}"
            )
        return False

    return True


if __name__ == "__main__":
    main()
