"""
Test data factories for creating test objects.

This module provides factory functions for creating test data objects
used in unit and integration tests.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, Optional
from uuid import UUID, uuid4

from backend.models.subscription import (
    SubscriptionAddon,
    SubscriptionPlan,
    TenantAddon,
    TenantSubscription,
)


def create_subscription_plan(
    id: Optional[UUID] = None,
    name: str = "Professional Plan",
    code: str = "professional",
    description: str = "Professional subscription plan",
    is_active: bool = True,
    is_public: bool = True,
    base_price_monthly: float = 99.99,
    base_price_yearly: float = 999.99,
    features: Optional[Dict[str, Any]] = None,
    created_at: Optional[datetime] = None,
    updated_at: Optional[datetime] = None,
) -> SubscriptionPlan:
    """
    Create a test subscription plan.
    
    Args:
        id: Plan ID (generates random if None)
        name: Plan name
        code: Plan code
        description: Plan description
        is_active: Whether plan is active
        is_public: Whether plan is public
        base_price_monthly: Monthly price
        base_price_yearly: Yearly price
        features: Plan features dict
        created_at: Creation timestamp
        updated_at: Update timestamp
        
    Returns:
        SubscriptionPlan: The created plan object
    """
    if features is None:
        features = {
            "feature_ai_research": True,
            "feature_case_management": True,
            "feature_calendar_booking": True,
            "feature_document_upload": True,
            "max_documents": 1000,
            "max_users": 10,
        }
    
    now = datetime.utcnow()
    
    return SubscriptionPlan(
        id=id or uuid4(),
        name=name,
        code=code,
        description=description,
        is_active=is_active,
        is_public=is_public,
        base_price_monthly=base_price_monthly,
        base_price_yearly=base_price_yearly,
        features=features,
        created_at=created_at or now,
        updated_at=updated_at or now,
    )


def create_subscription_addon(
    id: Optional[UUID] = None,
    name: str = "Voice Intake",
    code: str = "voice_intake",
    description: str = "AI-powered voice intake system",
    category: str = "ai_features",
    is_active: bool = True,
    price_monthly: float = 29.99,
    price_yearly: float = 299.99,
    features: Optional[Dict[str, Any]] = None,
    created_at: Optional[datetime] = None,
    updated_at: Optional[datetime] = None,
) -> SubscriptionAddon:
    """
    Create a test subscription addon.
    
    Args:
        id: Addon ID (generates random if None)
        name: Addon name
        code: Addon code
        description: Addon description
        category: Addon category
        is_active: Whether addon is active
        price_monthly: Monthly price
        price_yearly: Yearly price
        features: Addon features dict
        created_at: Creation timestamp
        updated_at: Update timestamp
        
    Returns:
        SubscriptionAddon: The created addon object
    """
    if features is None:
        features = {
            "feature_voice_intake": True,
            "max_voice_minutes": 500,
        }
    
    now = datetime.utcnow()
    
    return SubscriptionAddon(
        id=id or uuid4(),
        name=name,
        code=code,
        description=description,
        category=category,
        is_active=is_active,
        price_monthly=price_monthly,
        price_yearly=price_yearly,
        features=features,
        created_at=created_at or now,
        updated_at=updated_at or now,
    )


def create_tenant_subscription(
    id: Optional[UUID] = None,
    tenant_id: Optional[UUID] = None,
    plan_id: Optional[UUID] = None,
    status: str = "active",
    billing_cycle: str = "monthly",
    trial_start: Optional[datetime] = None,
    trial_end: Optional[datetime] = None,
    current_period_start: Optional[datetime] = None,
    current_period_end: Optional[datetime] = None,
    canceled_at: Optional[datetime] = None,
    payment_provider: Optional[str] = None,
    payment_provider_subscription_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    created_at: Optional[datetime] = None,
    updated_at: Optional[datetime] = None,
) -> TenantSubscription:
    """
    Create a test tenant subscription.
    
    Args:
        id: Subscription ID (generates random if None)
        tenant_id: Tenant ID (generates random if None)
        plan_id: Plan ID (generates random if None)
        status: Subscription status
        billing_cycle: Billing cycle
        trial_start: Trial start date
        trial_end: Trial end date
        current_period_start: Current period start
        current_period_end: Current period end
        canceled_at: Cancellation date
        payment_provider: Payment provider name
        payment_provider_subscription_id: Provider subscription ID
        metadata: Additional metadata
        created_at: Creation timestamp
        updated_at: Update timestamp
        
    Returns:
        TenantSubscription: The created subscription object
    """
    now = datetime.utcnow()
    
    if metadata is None:
        metadata = {}
    
    if current_period_start is None:
        current_period_start = now
    
    if current_period_end is None:
        if billing_cycle == "monthly":
            current_period_end = current_period_start + timedelta(days=30)
        else:  # yearly
            current_period_end = current_period_start + timedelta(days=365)
    
    return TenantSubscription(
        id=id or uuid4(),
        tenant_id=tenant_id or uuid4(),
        plan_id=plan_id or uuid4(),
        status=status,
        billing_cycle=billing_cycle,
        trial_start=trial_start,
        trial_end=trial_end,
        current_period_start=current_period_start,
        current_period_end=current_period_end,
        canceled_at=canceled_at,
        payment_provider=payment_provider,
        payment_provider_subscription_id=payment_provider_subscription_id,
        subscription_metadata=metadata,
        created_at=created_at or now,
        updated_at=updated_at or now,
    )


def create_tenant_addon(
    id: Optional[UUID] = None,
    tenant_id: Optional[UUID] = None,
    subscription_id: Optional[UUID] = None,
    addon_id: Optional[UUID] = None,
    status: str = "active",
    quantity: int = 1,
    current_period_start: Optional[datetime] = None,
    current_period_end: Optional[datetime] = None,
    canceled_at: Optional[datetime] = None,
    metadata: Optional[Dict[str, Any]] = None,
    created_at: Optional[datetime] = None,
    updated_at: Optional[datetime] = None,
) -> TenantAddon:
    """
    Create a test tenant addon.
    
    Args:
        id: Addon ID (generates random if None)
        tenant_id: Tenant ID (generates random if None)
        subscription_id: Subscription ID (generates random if None)
        addon_id: Addon ID (generates random if None)
        status: Addon status
        quantity: Addon quantity
        current_period_start: Current period start
        current_period_end: Current period end
        canceled_at: Cancellation date
        metadata: Additional metadata
        created_at: Creation timestamp
        updated_at: Update timestamp
        
    Returns:
        TenantAddon: The created tenant addon object
    """
    now = datetime.utcnow()
    
    if metadata is None:
        metadata = {}
    
    if current_period_start is None:
        current_period_start = now
    
    if current_period_end is None:
        current_period_end = current_period_start + timedelta(days=30)
    
    return TenantAddon(
        id=id or uuid4(),
        tenant_id=tenant_id or uuid4(),
        subscription_id=subscription_id or uuid4(),
        addon_id=addon_id or uuid4(),
        status=status,
        quantity=quantity,
        current_period_start=current_period_start,
        current_period_end=current_period_end,
        canceled_at=canceled_at,
        addon_metadata=metadata,
        created_at=created_at or now,
        updated_at=updated_at or now,
    )
