"""Test configuration and fixtures."""
import asyncio

# TODO: Restore when pi_lawyer.db.supabase_client exists
# from pi_lawyer.db.supabase_client import Any
# TODO: Restore when pi_lawyer.config exists
# from pi_lawyer.config import get_config
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict
from uuid import uuid4

import jwt
import pytest
from dotenv import load_dotenv

from src.pi_lawyer.db.supabase_client import SupabaseClient


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for each test session."""
    policy = asyncio.get_event_loop_policy()
    loop = policy.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def supabase_client():
    """Create Supabase client for testing."""
    load_dotenv()
    print("DEBUG: SUPABASE_URL:", os.getenv("SUPABASE_URL"))
    print("DEBUG: NEXT_PUBLIC_SUPABASE_URL:", os.getenv("NEXT_PUBLIC_SUPABASE_URL"))
    print("DEBUG: SUPABASE_KEY:", os.getenv("SUPABASE_KEY"))
    print("DEBUG: SUPABASE_SERVICE_KEY:", os.getenv("SUPABASE_SERVICE_KEY"))
    print(
        "DEBUG: NEXT_PUBLIC_SUPABASE_ANON_KEY:",
        os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY"),
    )
    print("DEBUG: SUPABASE_JWT_SECRET:", os.getenv("SUPABASE_JWT_SECRET"))

    url = os.getenv("SUPABASE_URL") or os.getenv("NEXT_PUBLIC_SUPABASE_URL")
    key = (
        os.getenv("SUPABASE_KEY")
        or os.getenv("SUPABASE_SERVICE_KEY")
        or os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY")
    )  # Use anon/service role key or service role key
    jwt_secret = os.getenv("SUPABASE_JWT_SECRET")

    if not url or not key or not jwt_secret:
        pytest.skip("Supabase credentials not found in environment")

    client = SupabaseClient(url, key, testing=True)
    client.jwt_secret = jwt_secret
    yield client


@pytest.fixture(scope="session")
async def test_tenant():
    """Create a test tenant for isolation."""
    tenant_id = str(uuid4())
    yield tenant_id


def generate_supabase_jwt(user_data: dict, jwt_secret: str) -> str:
    """Generate a valid Supabase JWT token for testing.

    Args:
        user_data: User data including id, role, and tenant_id
        jwt_secret: Supabase JWT secret for signing

    Returns:
        str: Signed JWT token
    """
    now = datetime.utcnow()

    payload = {
        # Required Supabase claims
        "aud": "authenticated",
        "iss": "supabase",
        "sub": user_data["id"],
        "email": user_data["email"],
        "phone": "",
        # Custom claims
        "role": user_data["role"],
        "tenant_id": user_data["tenant_id"],
        # Timestamps
        "iat": int(now.timestamp()),
        "exp": int((now + timedelta(hours=1)).timestamp()),
        # User metadata
        "user_metadata": {
            "role": user_data["role"],
            "tenant_id": user_data["tenant_id"],
        },
    }

    return jwt.encode(payload, jwt_secret, algorithm="HS256")


@pytest.fixture(scope="session")
async def test_users(supabase_client: Any, test_tenant: str):
    """Create test users with different roles."""
    users = {}
    roles = ["partner", "attorney", "paralegal", "staff"]

    # Create users with proper Supabase auth
    for role in roles:
        user_id = str(uuid4())
        email = f"{role}.{user_id}@example.com"

        user_data = {
            "id": user_id,
            "email": email,
            "role": role,
            "tenant_id": test_tenant,
        }

        # Generate proper Supabase JWT
        user_data["token"] = generate_supabase_jwt(
            user_data, supabase_client.jwt_secret
        )
        users[role] = user_data

        try:
            # First create auth user (requires service role key)
            auth_user = await supabase_client.client.auth.admin.create_user(
                {
                    "email": email,
                    "password": "test_password",
                    "email_confirm": True,
                    "user_metadata": {"role": role, "tenant_id": test_tenant},
                }
            )

            if not auth_user.user:
                raise ValueError(f"Failed to create auth user for {role}")

            # Then insert into users table with proper RLS
            await supabase_client.client.from_("users").insert(
                {
                    "id": user_id,
                    "email": email,
                    "role": role,
                    "tenant_id": test_tenant,
                    "auth_id": auth_user.user.id,
                }
            ).execute()

        except Exception as e:
            print(f"Failed to create {role} user: {str(e)}")
            continue

    if not users:
        pytest.skip("Failed to create any test users")

    yield users

    # Cleanup users
    for user_data in users.values():
        try:
            # Delete from auth
            await supabase_client.client.auth.admin.delete_user(user_data["id"])
            # User table deletion will cascade due to foreign key
        except Exception as e:
            print(f"Failed to delete user {user_data['id']}: {str(e)}")


@pytest.fixture(scope="session")
async def test_case(supabase_client: Any, test_users: Dict[str, Any]):
    """Create a test case with documents."""
    partner = test_users["partner"]

    # Create test case using partner token
    case_data = {
        "title": "Test Case",
        "description": "A test case for unit testing",
        "sensitive": False,
        "tenant_id": partner["tenant_id"],
        "created_by": partner["id"],
    }

    try:
        # Set auth token for partner
        supabase_client.client.auth.set_session(partner["token"])

        case = (
            await supabase_client.client.from_("cases")
            .insert(case_data)
            .select()
            .single()
            .execute()
        )
        if not case.data:
            pytest.skip("Failed to create test case")

        yield case.data

        # Cleanup case (will cascade delete related records)
        await supabase_client.client.from_("cases").delete().eq(
            "id", case.data["id"]
        ).execute()

    except Exception as e:
        print(f"Error in test case fixture: {str(e)}")
        pytest.skip(f"Test case setup failed: {str(e)}")
