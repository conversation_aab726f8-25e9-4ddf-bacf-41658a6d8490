from unittest import mock

from pi_lawyer.utils import check_gcs


def test_main_runs(monkeypatch):
    monkeypatch.setenv("GCS_SERVICE_ACCOUNT_FILE", "/tmp/fake.json")
    monkeypatch.setenv("GCS_BUCKET_NAME", "fake-bucket")
    with mock.patch("os.path.exists", return_value=True), mock.patch(
        "builtins.print"
    ) as mock_print:
        check_gcs.main()
        assert mock_print.called


# TODO: Add more granular checks for error cases and GCS client mocking.

# TODO: Add integration/external API tests

# Add more targeted tests when function signatures are known
