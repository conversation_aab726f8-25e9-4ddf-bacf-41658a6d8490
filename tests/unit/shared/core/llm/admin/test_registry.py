"""
Unit tests for the LLM Registry.
"""

import json
import os
import tempfile
import unittest
from unittest.mock import patch

from src.pi_lawyer.shared.core.llm.admin.models import ModelCapabilities, ModelConfig
from src.pi_lawyer.shared.core.llm.admin.registry import LLMRegistry, get_llm_registry
from src.pi_lawyer.shared.core.llm.config import LLMProvider


class TestLLMRegistry(unittest.TestCase):
    """Test the LLM Registry."""

    def setUp(self):
        """Set up the test."""
        # Create a temporary file for the registry
        self.temp_dir = tempfile.TemporaryDirectory()
        self.config_path = os.path.join(self.temp_dir.name, "llm_registry.json")
        
        # Patch the config path
        self.patcher = patch.object(
            LLMRegistry, "_config_path", 
            new_callable=unittest.mock.PropertyMock,
            return_value=self.config_path
        )
        self.patcher.start()
        
        # Clear the singleton instance
        LLMRegistry._instance = None
        
        # Create test models
        self.test_models = {
            LLMProvider.OPENAI: [
                ModelConfig(
                    provider=LLMProvider.OPENAI,
                    model_id="gpt-4o",
                    display_name="GPT-4o",
                    description="Test model",
                    capabilities=ModelCapabilities()
                ),
                ModelConfig(
                    provider=LLMProvider.OPENAI,
                    model_id="gpt-3.5-turbo",
                    display_name="GPT-3.5 Turbo",
                    description="Test model",
                    capabilities=ModelCapabilities()
                )
            ],
            LLMProvider.ANTHROPIC: [
                ModelConfig(
                    provider=LLMProvider.ANTHROPIC,
                    model_id="claude-3-opus",
                    display_name="Claude 3 Opus",
                    description="Test model",
                    capabilities=ModelCapabilities()
                ),
                ModelConfig(
                    provider=LLMProvider.ANTHROPIC,
                    model_id="claude-3-sonnet",
                    display_name="Claude 3 Sonnet",
                    description="Test model",
                    capabilities=ModelCapabilities()
                )
            ]
        }

    def tearDown(self):
        """Clean up after the test."""
        # Stop the patcher
        self.patcher.stop()
        
        # Clean up the temporary directory
        self.temp_dir.cleanup()
        
        # Clear the singleton instance
        LLMRegistry._instance = None

    @patch("src.pi_lawyer.shared.core.llm.admin.registry.get_available_models")
    def test_initialization(self, mock_get_available_models):
        """Test initialization of the registry."""
        # Set up the mock
        mock_get_available_models.side_effect = lambda provider: self.test_models.get(provider, [])
        
        # Create the registry
        registry = LLMRegistry()
        
        # Check that the registry was initialized
        self.assertTrue(registry._initialized)
        
        # Check that the default models were set
        self.assertIn("openai", registry._default_models)
        self.assertIn("anthropic", registry._default_models)
        
        # Check that the default models are the first ones in the list
        self.assertEqual(registry._default_models["openai"], "gpt-4o")
        self.assertEqual(registry._default_models["anthropic"], "claude-3-opus")
        
        # Check that the registry was saved
        self.assertTrue(os.path.exists(self.config_path))
        
        # Check the contents of the saved file
        with open(self.config_path, "r") as f:
            data = json.load(f)
            self.assertIn("default_models", data)
            self.assertIn("openai", data["default_models"])
            self.assertIn("anthropic", data["default_models"])
            self.assertEqual(data["default_models"]["openai"], "gpt-4o")
            self.assertEqual(data["default_models"]["anthropic"], "claude-3-opus")

    @patch("src.pi_lawyer.shared.core.llm.admin.registry.get_available_models")
    def test_get_default_model(self, mock_get_available_models):
        """Test getting the default model."""
        # Set up the mock
        mock_get_available_models.side_effect = lambda provider: self.test_models.get(provider, [])
        
        # Create the registry
        registry = LLMRegistry()
        
        # Check getting the default model
        self.assertEqual(registry.get_default_model("openai"), "gpt-4o")
        self.assertEqual(registry.get_default_model(LLMProvider.OPENAI), "gpt-4o")
        self.assertEqual(registry.get_default_model("anthropic"), "claude-3-opus")
        self.assertEqual(registry.get_default_model(LLMProvider.ANTHROPIC), "claude-3-opus")
        
        # Check getting a non-existent provider
        self.assertIsNone(registry.get_default_model("nonexistent"))

    @patch("src.pi_lawyer.shared.core.llm.admin.registry.get_available_models")
    def test_set_default_model(self, mock_get_available_models):
        """Test setting the default model."""
        # Set up the mock
        mock_get_available_models.side_effect = lambda provider: self.test_models.get(provider, [])
        
        # Create the registry
        registry = LLMRegistry()
        
        # Set the default model
        success = registry.set_default_model("openai", "gpt-3.5-turbo")
        
        # Check that the operation was successful
        self.assertTrue(success)
        
        # Check that the default model was updated
        self.assertEqual(registry.get_default_model("openai"), "gpt-3.5-turbo")
        
        # Check that the registry was saved
        with open(self.config_path, "r") as f:
            data = json.load(f)
            self.assertEqual(data["default_models"]["openai"], "gpt-3.5-turbo")
        
        # Try to set a non-existent model
        success = registry.set_default_model("openai", "nonexistent")
        
        # Check that the operation failed
        self.assertFalse(success)
        
        # Check that the default model was not updated
        self.assertEqual(registry.get_default_model("openai"), "gpt-3.5-turbo")

    @patch("src.pi_lawyer.shared.core.llm.admin.registry.get_available_models")
    def test_get_all_defaults(self, mock_get_available_models):
        """Test getting all default models."""
        # Set up the mock
        mock_get_available_models.side_effect = lambda provider: self.test_models.get(provider, [])
        
        # Create the registry
        registry = LLMRegistry()
        
        # Get all defaults
        defaults = registry.get_all_defaults()
        
        # Check that the defaults are correct
        self.assertIn("openai", defaults)
        self.assertIn("anthropic", defaults)
        self.assertEqual(defaults["openai"], "gpt-4o")
        self.assertEqual(defaults["anthropic"], "claude-3-opus")
        
        # Check that the returned dictionary is a copy
        defaults["openai"] = "modified"
        self.assertEqual(registry.get_default_model("openai"), "gpt-4o")

    @patch("src.pi_lawyer.shared.core.llm.admin.registry.get_available_models")
    def test_singleton(self, mock_get_available_models):
        """Test that the registry is a singleton."""
        # Set up the mock
        mock_get_available_models.side_effect = lambda provider: self.test_models.get(provider, [])
        
        # Create two registry instances
        registry1 = LLMRegistry()
        registry2 = LLMRegistry()
        
        # Check that they are the same instance
        self.assertIs(registry1, registry2)
        
        # Check that get_llm_registry returns the same instance
        registry3 = get_llm_registry()
        self.assertIs(registry1, registry3)
        
        # Modify the registry
        registry1.set_default_model("openai", "gpt-3.5-turbo")
        
        # Check that the change is reflected in all instances
        self.assertEqual(registry2.get_default_model("openai"), "gpt-3.5-turbo")
        self.assertEqual(registry3.get_default_model("openai"), "gpt-3.5-turbo")


if __name__ == "__main__":
    unittest.main()
