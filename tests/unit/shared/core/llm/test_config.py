"""
Unit tests for the LLM configuration models.
"""

import os
from unittest.mock import patch

from src.pi_lawyer.shared.core.llm.config import (
    EmbeddingConfig,
    LLMConfig,
    LLMMessage,
    LLMProvider,
    LLMResponse,
)


class TestLLMConfig:
    """Tests for the LLMConfig class."""

    @patch.dict(os.environ, {}, clear=True)
    def test_default_values(self):
        """Test default values for LLMConfig."""
        config = LLMConfig()

        assert config.provider == LLMProvider.OPENAI
        assert config.model == "gpt-4o"
        assert config.api_key is None
        assert config.temperature == 0.7
        assert config.max_tokens == 1000
        assert config.top_p == 1.0
        assert config.frequency_penalty == 0.0
        assert config.presence_penalty == 0.0
        assert config.stop_sequences is None
        assert config.timeout == 60.0
        assert config.max_retries == 3
        assert config.retry_delay == 1.0
        assert config.retry_backoff == 2.0

    def test_custom_values(self):
        """Test custom values for LLMConfig."""
        config = LLMConfig(
            provider=LLMProvider.ANTHROPIC,
            model="claude-3-opus-20240229",
            api_key="test-key",
            temperature=0.5,
            max_tokens=2000,
            top_p=0.9,
            frequency_penalty=0.1,
            presence_penalty=0.1,
            stop_sequences=["###"],
            timeout=30.0,
            max_retries=5,
            retry_delay=2.0,
            retry_backoff=3.0
        )

        assert config.provider == LLMProvider.ANTHROPIC
        assert config.model == "claude-3-opus-20240229"
        assert config.api_key == "test-key"
        assert config.temperature == 0.5
        assert config.max_tokens == 2000
        assert config.top_p == 0.9
        assert config.frequency_penalty == 0.1
        assert config.presence_penalty == 0.1
        assert config.stop_sequences == ["###"]
        assert config.timeout == 30.0
        assert config.max_retries == 5
        assert config.retry_delay == 2.0
        assert config.retry_backoff == 3.0

    def test_api_key_from_env(self):
        """Test getting API key from environment variables."""
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}):
            config = LLMConfig(provider=LLMProvider.OPENAI)
            assert config.api_key == "test-key"

        with patch.dict(os.environ, {"ANTHROPIC_API_KEY": "test-key"}):
            config = LLMConfig(provider=LLMProvider.ANTHROPIC)
            assert config.api_key == "test-key"

        with patch.dict(os.environ, {"GEMINI_API_KEY": "test-key"}):
            config = LLMConfig(provider=LLMProvider.GEMINI)
            assert config.api_key == "test-key"

        with patch.dict(os.environ, {"GROQ_API_KEY": "test-key"}):
            config = LLMConfig(provider=LLMProvider.GROQ)
            assert config.api_key == "test-key"

        with patch.dict(os.environ, {"VOYAGE_API_KEY": "test-key"}):
            config = LLMConfig(provider=LLMProvider.VOYAGE)
            assert config.api_key == "test-key"

    def test_api_key_from_env_var(self):
        """Test getting API key from custom environment variable."""
        with patch.dict(os.environ, {"CUSTOM_API_KEY": "test-key"}):
            config = LLMConfig(
                provider=LLMProvider.OPENAI,
                api_key_env_var="CUSTOM_API_KEY"
            )
            assert config.api_key == "test-key"


class TestLLMMessage:
    """Tests for the LLMMessage class."""

    def test_default_values(self):
        """Test default values for LLMMessage."""
        message = LLMMessage(content="Test message")

        assert message.role == "user"
        assert message.content == "Test message"

    def test_custom_values(self):
        """Test custom values for LLMMessage."""
        message = LLMMessage(
            role="system",
            content="You are a helpful assistant"
        )

        assert message.role == "system"
        assert message.content == "You are a helpful assistant"


class TestLLMResponse:
    """Tests for the LLMResponse class."""

    def test_default_values(self):
        """Test default values for LLMResponse."""
        response = LLMResponse(
            content="Test response",
            model="test-model"
        )

        assert response.content == "Test response"
        assert response.model == "test-model"
        assert response.usage == {}
        assert response.finish_reason is None

    def test_custom_values(self):
        """Test custom values for LLMResponse."""
        response = LLMResponse(
            content="Test response",
            model="test-model",
            usage={"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15},
            finish_reason="stop"
        )

        assert response.content == "Test response"
        assert response.model == "test-model"
        assert response.usage == {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15}
        assert response.finish_reason == "stop"


class TestEmbeddingConfig:
    """Tests for the EmbeddingConfig class."""

    @patch.dict(os.environ, {}, clear=True)
    def test_default_values(self):
        """Test default values for EmbeddingConfig."""
        config = EmbeddingConfig()

        assert config.provider == LLMProvider.OPENAI
        assert config.model == "text-embedding-3-large"
        assert config.api_key is None
        assert config.dimensions is None
        assert config.timeout == 60.0
        assert config.max_retries == 3
        assert config.retry_delay == 1.0
        assert config.retry_backoff == 2.0

    def test_custom_values(self):
        """Test custom values for EmbeddingConfig."""
        config = EmbeddingConfig(
            provider=LLMProvider.VOYAGE,
            model="voyage-3-large",
            api_key="test-key",
            dimensions=1536,
            timeout=30.0,
            max_retries=5,
            retry_delay=2.0,
            retry_backoff=3.0
        )

        assert config.provider == LLMProvider.VOYAGE
        assert config.model == "voyage-3-large"
        assert config.api_key == "test-key"
        assert config.dimensions == 1536
        assert config.timeout == 30.0
        assert config.max_retries == 5
        assert config.retry_delay == 2.0
        assert config.retry_backoff == 3.0

    def test_api_key_from_env(self):
        """Test getting API key from environment variables."""
        with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}):
            config = EmbeddingConfig(provider=LLMProvider.OPENAI)
            assert config.api_key == "test-key"

        with patch.dict(os.environ, {"VOYAGE_API_KEY": "test-key"}):
            config = EmbeddingConfig(provider=LLMProvider.VOYAGE)
            assert config.api_key == "test-key"
