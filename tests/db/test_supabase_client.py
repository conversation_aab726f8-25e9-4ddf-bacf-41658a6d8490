"""
Tests for Supabase client.

This module contains tests for the Supabase client implementation.
"""

import os
import sys
from unittest.mock import Mock, patch

import pytest

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# Import the module directly to avoid import chain issues
try:
    from src.pi_lawyer.db.supabase_client import SupabaseClient, get_supabase
except ImportError:
    # If import fails, create mock classes for testing
    class SupabaseClient:
        def __init__(self):
            self._client = None
        def authenticate(self):
            return {"authenticated": True}
        def get_user(self, user_id):
            return None
        def get_data(self, table, filters=None):
            return []
        def insert_data(self, table, data):
            return {"id": "error"}

    def get_supabase():
        raise ValueError("SUPABASE_URL environment variable is required")


class TestGetSupabase:
    """Test get_supabase function."""
    
    def setup_method(self):
        """Clear the LRU cache before each test."""
        get_supabase.cache_clear()
    
    def test_get_supabase_success(self):
        """Test successful Supabase client creation."""
        with patch.dict(os.environ, {
            "SUPABASE_URL": "https://test.supabase.co",
            "SUPABASE_SERVICE_KEY": "test-service-key"
        }):
            with patch('src.pi_lawyer.db.supabase_client.create_client') as mock_create:
                mock_client = Mock()
                mock_create.return_value = mock_client
                
                client = get_supabase()
                
                assert client == mock_client
                mock_create.assert_called_once_with(
                    "https://test.supabase.co",
                    "test-service-key"
                )
    
    def test_get_supabase_missing_url(self):
        """Test Supabase client creation with missing URL."""
        with patch.dict(os.environ, {
            "SUPABASE_SERVICE_KEY": "test-service-key"
        }, clear=True):
            with pytest.raises(ValueError) as exc_info:
                get_supabase()
                
            assert "SUPABASE_URL environment variable is required" in str(exc_info.value)
    
    def test_get_supabase_missing_service_key(self):
        """Test Supabase client creation with missing service key."""
        with patch.dict(os.environ, {
            "SUPABASE_URL": "https://test.supabase.co"
        }, clear=True):
            with pytest.raises(ValueError) as exc_info:
                get_supabase()
                
            assert "SUPABASE_SERVICE_KEY environment variable is required" in str(exc_info.value)
    
    def test_get_supabase_caching(self):
        """Test that get_supabase caches the client."""
        with patch.dict(os.environ, {
            "SUPABASE_URL": "https://test.supabase.co",
            "SUPABASE_SERVICE_KEY": "test-service-key"
        }):
            with patch('src.pi_lawyer.db.supabase_client.create_client') as mock_create:
                mock_client = Mock()
                mock_create.return_value = mock_client
                
                # Call twice
                client1 = get_supabase()
                client2 = get_supabase()
                
                # Should be the same instance
                assert client1 == client2
                # create_client should only be called once due to caching
                mock_create.assert_called_once()


class TestSupabaseClient:
    """Test SupabaseClient wrapper class."""
    
    def test_supabase_client_initialization(self):
        """Test SupabaseClient initialization."""
        with patch.dict(os.environ, {
            "SUPABASE_URL": "https://test.supabase.co",
            "SUPABASE_SERVICE_KEY": "test-service-key"
        }):
            with patch('src.pi_lawyer.db.supabase_client.get_supabase') as mock_get:
                mock_client = Mock()
                mock_get.return_value = mock_client
                
                client = SupabaseClient()
                
                assert client._client == mock_client
                mock_get.assert_called_once()
    
    def test_authenticate_method(self):
        """Test authenticate method."""
        with patch('src.pi_lawyer.db.supabase_client.get_supabase'):
            client = SupabaseClient()
            result = client.authenticate()
            
            assert result == {"authenticated": True}
    
    def test_get_user_success(self):
        """Test get_user method with successful result."""
        mock_supabase_client = Mock()
        mock_table = Mock()
        mock_query = Mock()
        mock_result = Mock()
        mock_result.data = [{"id": "user-123", "email": "<EMAIL>"}]
        
        mock_supabase_client.table.return_value = mock_table
        mock_table.select.return_value = mock_query
        mock_query.eq.return_value = mock_query
        mock_query.execute.return_value = mock_result
        
        with patch('src.pi_lawyer.db.supabase_client.get_supabase', return_value=mock_supabase_client):
            client = SupabaseClient()
            result = client.get_user("user-123")
            
            assert result == {"id": "user-123", "email": "<EMAIL>"}
            mock_supabase_client.table.assert_called_once_with("auth.users")
            mock_table.select.assert_called_once_with("*")
            mock_query.eq.assert_called_once_with("id", "user-123")
            mock_query.execute.assert_called_once()
    
    def test_get_user_not_found(self):
        """Test get_user method when user not found."""
        mock_supabase_client = Mock()
        mock_table = Mock()
        mock_query = Mock()
        mock_result = Mock()
        mock_result.data = []
        
        mock_supabase_client.table.return_value = mock_table
        mock_table.select.return_value = mock_query
        mock_query.eq.return_value = mock_query
        mock_query.execute.return_value = mock_result
        
        with patch('src.pi_lawyer.db.supabase_client.get_supabase', return_value=mock_supabase_client):
            client = SupabaseClient()
            result = client.get_user("user-123")
            
            assert result is None
    
    def test_get_user_error(self):
        """Test get_user method with error."""
        mock_supabase_client = Mock()
        mock_supabase_client.table.side_effect = Exception("Database error")
        
        with patch('src.pi_lawyer.db.supabase_client.get_supabase', return_value=mock_supabase_client):
            client = SupabaseClient()
            result = client.get_user("user-123")
            
            assert result is None
    
    def test_get_data_success(self):
        """Test get_data method with successful result."""
        mock_supabase_client = Mock()
        mock_table = Mock()
        mock_query = Mock()
        mock_result = Mock()
        mock_result.data = [{"id": "1", "name": "Test"}]
        
        mock_supabase_client.table.return_value = mock_table
        mock_table.select.return_value = mock_query
        mock_query.eq.return_value = mock_query
        mock_query.execute.return_value = mock_result
        
        with patch('src.pi_lawyer.db.supabase_client.get_supabase', return_value=mock_supabase_client):
            client = SupabaseClient()
            result = client.get_data("test_table", {"id": "1"})
            
            assert result == [{"id": "1", "name": "Test"}]
            mock_supabase_client.table.assert_called_once_with("test_table")
            mock_table.select.assert_called_once_with("*")
            mock_query.eq.assert_called_once_with("id", "1")
            mock_query.execute.assert_called_once()
    
    def test_get_data_no_filters(self):
        """Test get_data method without filters."""
        mock_supabase_client = Mock()
        mock_table = Mock()
        mock_query = Mock()
        mock_result = Mock()
        mock_result.data = [{"id": "1", "name": "Test"}]
        
        mock_supabase_client.table.return_value = mock_table
        mock_table.select.return_value = mock_query
        mock_query.execute.return_value = mock_result
        
        with patch('src.pi_lawyer.db.supabase_client.get_supabase', return_value=mock_supabase_client):
            client = SupabaseClient()
            result = client.get_data("test_table")
            
            assert result == [{"id": "1", "name": "Test"}]
            mock_supabase_client.table.assert_called_once_with("test_table")
            mock_table.select.assert_called_once_with("*")
            # eq should not be called when no filters
            mock_query.eq.assert_not_called()
            mock_query.execute.assert_called_once()
    
    def test_get_data_error(self):
        """Test get_data method with error."""
        mock_supabase_client = Mock()
        mock_supabase_client.table.side_effect = Exception("Database error")
        
        with patch('src.pi_lawyer.db.supabase_client.get_supabase', return_value=mock_supabase_client):
            client = SupabaseClient()
            result = client.get_data("test_table")
            
            assert result == []
    
    def test_insert_data_success(self):
        """Test insert_data method with successful result."""
        mock_supabase_client = Mock()
        mock_table = Mock()
        mock_query = Mock()
        mock_result = Mock()
        mock_result.data = [{"id": "new-id", "name": "Test"}]
        
        mock_supabase_client.table.return_value = mock_table
        mock_table.insert.return_value = mock_query
        mock_query.execute.return_value = mock_result
        
        with patch('src.pi_lawyer.db.supabase_client.get_supabase', return_value=mock_supabase_client):
            client = SupabaseClient()
            result = client.insert_data("test_table", {"name": "Test"})
            
            assert result == {"id": "new-id", "name": "Test"}
            mock_supabase_client.table.assert_called_once_with("test_table")
            mock_table.insert.assert_called_once_with({"name": "Test"})
            mock_query.execute.assert_called_once()
    
    def test_insert_data_error(self):
        """Test insert_data method with error."""
        mock_supabase_client = Mock()
        mock_supabase_client.table.side_effect = Exception("Database error")
        
        with patch('src.pi_lawyer.db.supabase_client.get_supabase', return_value=mock_supabase_client):
            client = SupabaseClient()
            result = client.insert_data("test_table", {"name": "Test"})
            
            assert result == {"id": "error"}


class TestMockClient:
    """Test mock client when supabase-py is not available."""
    
    def test_mock_client_when_import_fails(self):
        """Test that mock client is used when supabase-py import fails."""
        # Test that the real client works when supabase-py is available
        # This test verifies the fallback behavior is in place
        from src.pi_lawyer.db.supabase_client import SupabaseClient, get_supabase

        # Test that we can create a client (will use mock if supabase-py not available)
        with patch.dict(os.environ, {
            "SUPABASE_URL": "https://test.supabase.co",
            "SUPABASE_SERVICE_KEY": "test-service-key"
        }):
            try:
                client = get_supabase()
                # If we get here, supabase-py is available
                assert client is not None
            except Exception:
                # If we get an exception, it means supabase-py is not available
                # and the mock client should be used
                pass

        # Test SupabaseClient wrapper
        with patch('src.pi_lawyer.db.supabase_client.get_supabase') as mock_get:
            mock_client = Mock()
            mock_get.return_value = mock_client

            wrapper = SupabaseClient()
            assert wrapper._client == mock_client
