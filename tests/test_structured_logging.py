from pi_lawyer.utils import structured_logging


def test_structured_logging_module_imports():
    # Module should export configure_logging and clear_context
    assert hasattr(structured_logging, "configure_logging")


def test_context_set_get_clear():
    structured_logging.set_correlation_id("cid")
    assert structured_logging.get_correlation_id() == "cid"
    structured_logging.clear_context()
    # Clearing context resets correlation ID; new ID should differ from the old one
    new_cid = structured_logging.get_correlation_id()
    assert new_cid != "cid" and isinstance(new_cid, str)


# TODO: Test configure_logging and log_with_context.

# Add more targeted tests when function signatures are known
