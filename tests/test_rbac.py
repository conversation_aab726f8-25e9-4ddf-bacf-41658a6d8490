"""Test Role-Based Access Control functionality."""
import os
from uuid import uuid4

import psycopg2
import pytest
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@pytest.fixture
def db_connection():
    """Create database connection fixture."""
    connection = psycopg2.connect(
        user=os.getenv("DB_USER"),
        password=os.getenv("DB_PASSWORD"),
        host=os.getenv("DB_HOST"),
        port=os.getenv("DB_PORT"),
        dbname=os.getenv("DB_NAME"),
    )
    yield connection
    connection.close()


@pytest.fixture
def test_tenant(db_connection):
    """Create a test tenant."""
    tenant_id = str(uuid4())
    cursor = db_connection.cursor()
    # Defer FK constraint checks until commit
    cursor.execute("SET CONSTRAINTS ALL DEFERRED;")
    # Create test tenant user (firm owner)
    owner_id = str(uuid4())
    cursor.execute(
        "INSERT INTO tenants.users (id, email, role, tenant_id) VALUES (%s, %s, %s, %s)",
        (owner_id, f"owner_{tenant_id}@test.com", "partner", tenant_id),
    )
    # Create firm record for this tenant
    cursor.execute(
        "INSERT INTO tenants.firms (tenant_id, name, state_bar_number, firm_type, primary_email, phone, admin_user_id) "
        "VALUES (%s, %s, %s, %s, %s, %s, %s)",
        (
            tenant_id,
            "TestFirm",
            f"unique_{tenant_id}",
            "Solo Practice",
            f"owner_{tenant_id}@test.com",
            "************",
            owner_id,
        ),
    )
    db_connection.commit()
    cursor.close()
    return tenant_id


@pytest.fixture
def test_users(db_connection, test_tenant):
    """Create test users with different roles."""
    cursor = db_connection.cursor()
    users = {}

    for role in ["partner", "attorney", "paralegal", "staff"]:
        user_id = str(uuid4())
        email = f"{role}_{user_id}@test.com"

        cursor.execute(
            """
            INSERT INTO tenants.users (id, email, role, tenant_id)
            VALUES (%s, %s, %s, %s)
            RETURNING id, email, role, tenant_id;
        """,
            (user_id, email, role, test_tenant),
        )

        user = dict(zip(["id", "email", "role", "tenant_id"], cursor.fetchone(), strict=False))
        users[role] = user

    db_connection.commit()
    cursor.close()
    return users


@pytest.fixture
def test_case(db_connection, test_users):
    """Create a test case."""
    cursor = db_connection.cursor()
    case_id = str(uuid4())
    partner = test_users["partner"]
    # Create a dummy client for the case
    client_id = str(uuid4())
    cursor.execute(
        "INSERT INTO tenants.clients (id, tenant_id, first_name, last_name, client_type) VALUES (%s, %s, %s, %s, %s)",
        (client_id, partner["tenant_id"], "Test", "Client", "individual"),
    )
    # Create test case with explicit status
    cursor.execute(
        "INSERT INTO tenants.cases (id, tenant_id, title, description, created_by, status, client_id) VALUES (%s, %s, %s, %s, %s, %s, %s)",
        (
            case_id,
            partner["tenant_id"],
            "Test Case",
            "Test case description",
            partner["id"],
            "active",
            client_id,
        ),
    )
    # Create test document
    doc_id = str(uuid4())
    cursor.execute(
        "INSERT INTO tenants.case_documents (id, tenant_id, case_id, title, document_type, created_by) VALUES (%s, %s, %s, %s, %s, %s)",
        (doc_id, partner["tenant_id"], case_id, "Test Document", "memo", partner["id"]),
    )
    db_connection.commit()
    cursor.close()
    return {"case_id": case_id, "document_id": doc_id, "client_id": client_id}


def test_user_context_retrieval(db_connection, test_users):
    """Test that user context is correctly retrieved for different roles."""
    cursor = db_connection.cursor()

    for role, user in test_users.items():
        cursor.execute(
            """
            SELECT role, tenant_id
            FROM tenants.users
            WHERE id = %s;
        """,
            (user["id"],),
        )

        context = dict(zip(["role", "tenant_id"], cursor.fetchone(), strict=False))
        assert context["role"] == role
        assert context["tenant_id"] == user["tenant_id"]

    cursor.close()


def test_partner_access(db_connection, test_users, test_case):
    """Test that partners have full access to all cases."""
    cursor = db_connection.cursor()
    partner = test_users["partner"]

    # Should be able to view case documents
    cursor.execute(
        """
        SELECT COUNT(*)
        FROM tenants.case_documents
        WHERE case_id = %s AND tenant_id = %s;
    """,
        (test_case["case_id"], partner["tenant_id"]),
    )

    count = cursor.fetchone()[0]
    assert count > 0, "Partner should be able to view case documents"

    # Should be able to create cases linked to an existing client
    new_case_id = str(uuid4())
    cursor.execute(
        "INSERT INTO tenants.cases (id, tenant_id, title, description, created_by, status, client_id)"
        " VALUES (%s, %s, %s, %s, %s, %s, %s) RETURNING id;",
        (
            new_case_id,
            partner["tenant_id"],
            "New Test Case",
            "Created by partner",
            partner["id"],
            "active",
            test_case["client_id"],
        ),
    )
    db_connection.commit()
    assert cursor.fetchone() is not None, "Partner should be able to create cases"

    cursor.close()


def test_attorney_access(db_connection, test_users, test_case):
    """Test attorney access restrictions."""
    cursor = db_connection.cursor()
    attorney = test_users["attorney"]

    # Should be able to create cases
    new_case_id = str(uuid4())
    cursor.execute(
        "INSERT INTO tenants.cases (id, tenant_id, title, description, created_by, status, client_id)"
        " VALUES (%s, %s, %s, %s, %s, %s, %s) RETURNING id;",
        (
            new_case_id,
            attorney["tenant_id"],
            "Attorney Test Case",
            "Created by attorney",
            attorney["id"],
            "active",
            test_case["client_id"],
        ),
    )
    db_connection.commit()
    assert cursor.fetchone() is not None, "Attorney should be able to create cases"

    cursor.close()


def test_paralegal_access(db_connection, test_users, test_case):
    """Test paralegal access restrictions."""
    cursor = db_connection.cursor()
    paralegal = test_users["paralegal"]
    partner = test_users["partner"]

    # Should NOT be able to view case without assignment
    cursor.execute(
        """
        SELECT COUNT(*)
        FROM tenants.case_documents
        WHERE case_id = %s
        AND tenant_id = %s
        AND EXISTS (
            SELECT 1 FROM tenants.assignments
            WHERE case_id = %s
            AND user_id = %s
        );
    """,
        (
            test_case["case_id"],
            paralegal["tenant_id"],
            test_case["case_id"],
            paralegal["id"],
        ),
    )

    count = cursor.fetchone()[0]
    assert count == 0, "Paralegal should not see documents without assignment"

    # Assign paralegal to case
    cursor.execute(
        "INSERT INTO tenants.assignments (id, tenant_id, case_id, user_id, assigned_by, role)"
        " VALUES (%s, %s, %s, %s, %s, %s);",
        (
            str(uuid4()),
            paralegal["tenant_id"],
            test_case["case_id"],
            paralegal["id"],
            partner["id"],
            "support",
        ),
    )
    db_connection.commit()

    # Now should be able to view
    cursor.execute(
        """
        SELECT COUNT(*)
        FROM tenants.case_documents
        WHERE case_id = %s
        AND tenant_id = %s
        AND EXISTS (
            SELECT 1 FROM tenants.assignments
            WHERE case_id = %s
            AND user_id = %s
        );
    """,
        (
            test_case["case_id"],
            paralegal["tenant_id"],
            test_case["case_id"],
            paralegal["id"],
        ),
    )

    count = cursor.fetchone()[0]
    assert count > 0, "Paralegal should see documents after assignment"

    cursor.close()


def test_staff_access(db_connection, test_users, test_case):
    """Test staff access restrictions."""
    cursor = db_connection.cursor()
    staff = test_users["staff"]

    # Should NOT be able to view any case documents
    cursor.execute(
        """
        SELECT COUNT(*)
        FROM tenants.case_documents
        WHERE case_id = %s AND tenant_id = %s
          AND EXISTS (
              SELECT 1 FROM tenants.assignments
              WHERE case_id = %s AND user_id = %s
          );
    """,
        (test_case["case_id"], staff["tenant_id"], test_case["case_id"], staff["id"]),
    )

    count = cursor.fetchone()[0]
    assert count == 0, "Staff should not be able to view case documents"

    cursor.close()


def test_cross_tenant_isolation(db_connection, test_users, test_case):
    """Test that users cannot access data from other tenants."""
    cursor = db_connection.cursor()

    # Create user in different tenant
    other_tenant_id = str(uuid4())
    # Skipping inserting a user for the other tenant to avoid FK constraints

    # Try to access case from different tenant
    cursor.execute(
        """
        SELECT COUNT(*)
        FROM tenants.case_documents
        WHERE case_id = %s AND tenant_id = %s;
    """,
        (test_case["case_id"], other_tenant_id),
    )

    count = cursor.fetchone()[0]
    assert count == 0, "User should not see documents from other tenant"

    db_connection.commit()
    cursor.close()


def test_access_logging(db_connection, test_users, test_case):
    """Test that access to resources is properly logged."""
    cursor = db_connection.cursor()
    partner = test_users["partner"]

    # Log document access
    cursor.execute(
        """
        INSERT INTO tenants.access_logs (
            id, tenant_id, user_id, resource_type, resource_id, action
        ) VALUES (%s, %s, %s, %s, %s, %s);
    """,
        (
            str(uuid4()),
            partner["tenant_id"],
            partner["id"],
            "document",
            test_case["document_id"],
            "view",
        ),
    )

    # Check logs
    cursor.execute(
        """
        SELECT COUNT(*)
        FROM tenants.access_logs
        WHERE user_id = %s
        AND resource_id = %s;
    """,
        (partner["id"], test_case["document_id"]),
    )

    count = cursor.fetchone()[0]
    assert count > 0, "Access should be logged"

    db_connection.commit()
    cursor.close()
