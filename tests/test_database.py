"""Test database connection and schema setup."""
import os

import psycopg2
import pytest
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()


@pytest.fixture
def db_connection():
    """Create database connection fixture."""
    connection = psycopg2.connect(
        user=os.getenv("DB_USER"),
        password=os.getenv("DB_PASSWORD"),
        host=os.getenv("DB_HOST"),
        port=os.getenv("DB_PORT"),
        dbname=os.getenv("DB_NAME"),
    )
    yield connection
    connection.close()


def test_database_connection(db_connection):
    """Test basic database connectivity."""
    cursor = db_connection.cursor()
    cursor.execute("SELECT NOW();")
    result = cursor.fetchone()
    assert result is not None
    cursor.close()


def test_schema_existence(db_connection):
    """Test that required schemas exist."""
    cursor = db_connection.cursor()

    # Check public schema
    cursor.execute(
        """
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name IN ('public', 'tenants');
    """
    )
    schemas = [row[0] for row in cursor.fetchall()]
    assert "public" in schemas, "Public schema not found"
    assert "tenants" in schemas, "Tenants schema not found"

    cursor.close()


def test_public_tables(db_connection):
    """Test that public schema has required tables."""
    cursor = db_connection.cursor()

    cursor.execute(
        """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name IN ('documents', 'chunks');
    """
    )
    tables = [row[0] for row in cursor.fetchall()]
    assert "documents" in tables, "Documents table not found in public schema"
    assert "chunks" in tables, "Chunks table not found in public schema"

    cursor.close()


def test_tenant_tables(db_connection):
    """Test that tenants schema has required tables."""
    cursor = db_connection.cursor()

    cursor.execute(
        """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'tenants'
        AND table_name IN (
            'users', 'cases', 'case_documents',
            'document_chunks', 'assignments', 'access_logs'
        );
    """
    )
    tables = [row[0] for row in cursor.fetchall()]
    expected_tables = {
        "users",
        "cases",
        "case_documents",
        "document_chunks",
        "assignments",
        "access_logs",
    }
    missing_tables = expected_tables - set(tables)
    assert not missing_tables, f"Missing tables in tenants schema: {missing_tables}"

    cursor.close()
