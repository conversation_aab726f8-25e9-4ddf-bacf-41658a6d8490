from unittest import mock

import pytest

from pi_lawyer.utils.query_classifier import QueryClassifier


def test_has_legal_keywords():
    qc = QueryClassifier()
    assert qc.has_legal_keywords("What is a contract?")
    assert not qc.has_legal_keywords("How to bake bread?")


@pytest.mark.asyncio
async def test_classify_with_groq(monkeypatch):
    monkeypatch.setenv("GROQ_API_KEY", "fake-key")
    qc = QueryClassifier()
    with mock.patch.object(qc, "classify_with_groq", return_value=(True, 0.9)):
        result = await qc.classify_with_groq("test")
        assert result[0] is True


# TODO: Add real API mock and test classify_with_gemini/is_legal_query.

# Add more targeted tests when function signatures are known
