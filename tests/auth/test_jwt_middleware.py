"""
Tests for JWT middleware.

This module contains tests for the JWT authentication middleware.
"""

import os
import sys
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import jwt
import pytest

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from fastapi import HTTPException, Request

from backend.middleware.auth_middleware import (
    UserContext,
    get_current_user,
    validate_jwt,
)


class TestValidateJWT:
    """Test JWT validation function."""
    
    def test_validate_jwt_success(self):
        """Test successful JWT validation."""
        # Create a test JWT
        secret = "test-secret"
        payload = {
            "sub": "user-123",
            "email": "<EMAIL>",
            "tenant_id": "tenant-123",
            "role": "attorney",
            "permissions": ["read", "write"],
            "exp": datetime.utcnow() + timedelta(hours=1),
            "iat": datetime.utcnow()
        }
        
        token = jwt.encode(payload, secret, algorithm="HS256")
        
        with patch.dict(os.environ, {"SUPABASE_JWT_SECRET": secret}):
            claims = validate_jwt(token)
            
        assert claims["sub"] == "user-123"
        assert claims["email"] == "<EMAIL>"
        assert claims["tenant_id"] == "tenant-123"
        assert claims["role"] == "attorney"
        assert claims["permissions"] == ["read", "write"]
    
    def test_validate_jwt_expired_token(self):
        """Test JWT validation with expired token."""
        secret = "test-secret"
        payload = {
            "sub": "user-123",
            "exp": datetime.utcnow() - timedelta(hours=1),  # Expired
            "iat": datetime.utcnow() - timedelta(hours=2)
        }
        
        token = jwt.encode(payload, secret, algorithm="HS256")
        
        with patch.dict(os.environ, {"SUPABASE_JWT_SECRET": secret}):
            with pytest.raises(jwt.ExpiredSignatureError):
                validate_jwt(token)
    
    def test_validate_jwt_invalid_signature(self):
        """Test JWT validation with invalid signature."""
        secret = "test-secret"
        wrong_secret = "wrong-secret"
        payload = {
            "sub": "user-123",
            "exp": datetime.utcnow() + timedelta(hours=1),
            "iat": datetime.utcnow()
        }
        
        token = jwt.encode(payload, wrong_secret, algorithm="HS256")
        
        with patch.dict(os.environ, {"SUPABASE_JWT_SECRET": secret}):
            with pytest.raises(jwt.InvalidSignatureError):
                validate_jwt(token)
    
    def test_validate_jwt_no_secret_development(self):
        """Test JWT validation with no secret in development mode."""
        payload = {
            "sub": "user-123",
            "exp": datetime.utcnow() + timedelta(hours=1),
            "iat": datetime.utcnow()
        }
        
        # Use default development secret
        token = jwt.encode(payload, "0123456789abcdef0123456789abcdef", algorithm="HS256")
        
        with patch.dict(os.environ, {"APP_ENV": "development"}, clear=True):
            claims = validate_jwt(token)
            
        assert claims["sub"] == "user-123"
    
    def test_validate_jwt_no_secret_production(self):
        """Test JWT validation with no secret in production mode."""
        with patch.dict(os.environ, {"APP_ENV": "production"}, clear=True):
            with pytest.raises(jwt.PyJWTError):
                validate_jwt("dummy-token")


class TestGetCurrentUser:
    """Test get_current_user dependency function."""
    
    @pytest.fixture
    def mock_request(self):
        """Create a mock request object."""
        request = Mock(spec=Request)
        request.headers = {}
        request.state = Mock()
        return request
    
    async def test_get_current_user_from_state(self):
        """Test getting user from request state."""
        request = Mock(spec=Request)
        request.state = Mock()

        # Mock user already in state
        user_context = UserContext(
            user_id="user-123",
            email="<EMAIL>",
            role="attorney",
            firm_id="firm-123",
            permissions=["read", "write"],
            is_authenticated=True
        )
        request.state.user = user_context

        result = await get_current_user(request)
        assert result == user_context
    
    async def test_get_current_user_from_jwt_token(self):
        """Test getting user from JWT token."""
        request = Mock(spec=Request)
        request.headers = {
            "Authorization": "Bearer valid-token"
        }
        # Create a mock state without a user attribute
        request.state = Mock()
        del request.state.user  # Remove the user attribute to simulate no cached user

        # Mock validate_jwt to return claims
        claims = {
            "sub": "user-123",
            "email": "<EMAIL>",
            "tenant_id": "firm-123",
            "role": "attorney",
            "permissions": ["read", "write"]
        }

        with patch('backend.middleware.auth_middleware.validate_jwt', return_value=claims):
            result = await get_current_user(request)

        assert result.user_id == "user-123"
        assert result.email == "<EMAIL>"
        assert result.firm_id == "firm-123"
        assert result.role == "attorney"
        assert result.permissions == ["read", "write"]
        assert result.is_authenticated is True
    
    async def test_get_current_user_no_authorization_header(self):
        """Test getting user with no authorization header."""
        request = Mock(spec=Request)
        request.headers = {}
        request.state = Mock()
        del request.state.user  # Remove the user attribute to simulate no cached user

        with pytest.raises(HTTPException) as exc_info:
            await get_current_user(request)

        assert exc_info.value.status_code == 401
        assert "Authentication required" in str(exc_info.value.detail)
    
    async def test_get_current_user_invalid_authorization_header(self):
        """Test getting user with invalid authorization header."""
        request = Mock(spec=Request)
        request.headers = {
            "Authorization": "Invalid header"
        }
        request.state = Mock()
        del request.state.user  # Remove the user attribute to simulate no cached user

        with pytest.raises(HTTPException) as exc_info:
            await get_current_user(request)

        assert exc_info.value.status_code == 401
        assert "Authentication required" in str(exc_info.value.detail)
    
    async def test_get_current_user_missing_tenant_id(self):
        """Test getting user with JWT missing tenant_id."""
        request = Mock(spec=Request)
        request.headers = {
            "Authorization": "Bearer valid-token"
        }
        request.state = Mock()
        del request.state.user  # Remove the user attribute to simulate no cached user

        # Mock validate_jwt to return claims without tenant_id
        claims = {
            "sub": "user-123",
            "email": "<EMAIL>",
            "role": "attorney"
        }

        with patch('backend.middleware.auth_middleware.validate_jwt', return_value=claims):
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user(request)

        assert exc_info.value.status_code == 401
        assert "missing tenant_id" in str(exc_info.value.detail)
    
    async def test_get_current_user_invalid_jwt(self):
        """Test getting user with invalid JWT."""
        request = Mock(spec=Request)
        request.headers = {
            "Authorization": "Bearer invalid-token"
        }
        request.state = Mock()
        del request.state.user  # Remove the user attribute to simulate no cached user

        with patch('backend.middleware.auth_middleware.validate_jwt', side_effect=jwt.PyJWTError("Invalid token")):
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user(request)

        assert exc_info.value.status_code == 401
        assert "Invalid authentication token" in str(exc_info.value.detail)


class TestUserContext:
    """Test UserContext model."""
    
    def test_user_context_creation(self):
        """Test creating a UserContext instance."""
        user = UserContext(
            user_id="user-123",
            email="<EMAIL>",
            role="attorney",
            firm_id="firm-123",
            permissions=["read", "write"],
            is_authenticated=True
        )
        
        assert user.user_id == "user-123"
        assert user.email == "<EMAIL>"
        assert user.role == "attorney"
        assert user.firm_id == "firm-123"
        assert user.permissions == ["read", "write"]
        assert user.is_authenticated is True
    
    def test_user_context_defaults(self):
        """Test UserContext with default values."""
        user = UserContext(
            user_id="user-123",
            firm_id="firm-123"
        )
        
        assert user.user_id == "user-123"
        assert user.firm_id == "firm-123"
        assert user.email is None
        assert user.role == "user"
        assert user.permissions == []
        assert user.is_authenticated is True
