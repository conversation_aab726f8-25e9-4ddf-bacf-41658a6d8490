import os
import sys
import unittest

# Add the parent directory to the path so we can import the pi_lawyer module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pi_lawyer.utils.template_utils import (
    categorize_template,
    compile_template_sections,
    extract_variables,
    get_template_by_state_and_type,
    process_conditionals,
    process_loops,
    render_template,
)


class TestTemplateUtils(unittest.TestCase):
    def test_extract_variables(self):
        """Test that variables are correctly extracted from a template."""
        template = """
        This is a template with {{client_name}} and {{attorney_name}}.
        The incident occurred on {{incident_date}}.
        """
        variables = extract_variables(template)
        self.assertEqual(
            set(variables), {"client_name", "attorney_name", "incident_date"}
        )

    def test_render_template(self):
        """Test that templates are correctly rendered with variables."""
        template = "Hello, {{name}}!"
        variables = {"name": "<PERSON>"}
        rendered = render_template(template, variables)
        self.assertEqual(rendered, "Hello, <PERSON>!")

    def test_process_conditionals(self):
        """Test that conditionals are correctly processed."""
        template = """
        {% if has_injuries %}
        Client suffered {{injury_description}}.
        {% else %}
        Client did not report any injuries.
        {% endif %}
        """

        # Test with condition true
        variables = {"has_injuries": True, "injury_description": "a broken arm"}
        rendered = process_conditionals(template, variables)
        self.assertIn("Client suffered", rendered)
        self.assertNotIn("Client did not report any injuries", rendered)

        # Test with condition false
        variables = {"has_injuries": False}
        rendered = process_conditionals(template, variables)
        self.assertNotIn("Client suffered", rendered)
        self.assertIn("Client did not report any injuries", rendered)

    def test_process_loops(self):
        """Test that loops are correctly processed."""
        template = """
        Injuries:
        {% for injury in injuries %}
        - {{injury}}
        {% endfor %}
        """

        variables = {"injuries": ["Broken arm", "Concussion", "Whiplash"]}
        rendered = process_loops(template, variables)

        for injury in variables["injuries"]:
            self.assertIn(f"- {injury}", rendered)

    def test_categorize_template(self):
        """Test that templates are correctly categorized."""
        template_data = {
            "name": "Client Intake Form",
            "state": "Texas",
            "practice_area": "Personal Injury",
            "category": "Client Intake & Engagement",
        }

        category_path = categorize_template(template_data)
        self.assertEqual(category_path["state"], "Texas")
        self.assertEqual(category_path["practice_area"], "Personal Injury")
        self.assertEqual(category_path["category"], "Client Intake & Engagement")

    # Skip the async tests for now
    @unittest.skip("Skipping async tests")
    def test_fetch_template_from_supabase(self):
        """Test wrapper for async test."""
        pass

    @unittest.skip("Skipping async tests")
    def test_save_template_to_supabase(self):
        """Test wrapper for async test."""
        pass

    def test_compile_template_sections(self):
        """Test that template sections are correctly compiled."""
        sections = [
            {"title": "HEADER", "content": "This is the header.", "order": 1},
            {"title": "BODY", "content": "This is the body.", "order": 2},
            {"title": "FOOTER", "content": "This is the footer.", "order": 3},
        ]

        variables = {}
        compiled = compile_template_sections(sections, variables)
        self.assertIn("This is the header.", compiled)
        self.assertIn("This is the body.", compiled)
        self.assertIn("This is the footer.", compiled)

    def test_get_template_by_state_and_type(self):
        """Test that templates are correctly retrieved by state and type."""
        # Create sample templates
        templates = [
            {
                "id": "789",
                "name": "demand letter",
                "content": "This is a Texas demand letter for {{client_name}}.",
                "state": "Texas",
                "practice_area": "Personal Injury",
                "category": "Pre-Litigation",
            },
            {
                "id": "790",
                "name": "demand letter",
                "content": "This is a General demand letter for {{client_name}}.",
                "state": "General",
                "practice_area": "Personal Injury",
                "category": "Pre-Litigation",
            },
        ]

        # Test state-specific template
        template = get_template_by_state_and_type(
            "Texas", "Personal Injury", "demand letter", templates
        )
        self.assertEqual(template["id"], "789")
        self.assertEqual(template["state"], "Texas")

        # Test fallback to general template
        template = get_template_by_state_and_type(
            "California", "Personal Injury", "demand letter", templates
        )
        self.assertEqual(template["id"], "790")
        self.assertEqual(template["state"], "General")


if __name__ == "__main__":
    unittest.main()
