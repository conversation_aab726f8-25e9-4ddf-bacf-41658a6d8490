"""
CI tests for Prometheus metrics functionality.

This module contains tests that can run in CI to verify metrics functionality
without requiring the full FastAPI application stack.
"""

import os
import sys
import time

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_metrics_endpoint_content():
    """Test that metrics endpoint returns expected metric names."""
    try:
        from backend.metrics import (
            calendar_api_requests_total,
            calendar_api_response_time,
            get_metrics_output,
        )
        
        # Increment some metrics to ensure they appear in output
        calendar_api_requests_total.labels(
            provider="google",
            endpoint="connect",
            status="success"
        ).inc()
        
        calendar_api_requests_total.labels(
            provider="microsoft",
            endpoint="callback",
            status="error"
        ).inc()
        
        calendar_api_response_time.labels(provider="google").observe(0.5)
        calendar_api_response_time.labels(provider="microsoft").observe(1.2)
        
        # Generate metrics output
        output = get_metrics_output()
        output_str = output.decode('utf-8')
        
        # Verify the two new metric names are present
        assert "calendar_api_requests_total" in output_str or "calendar_api_requests" in output_str
        assert "calendar_api_response_time" in output_str
        
        # Verify help text is present
        assert "Calendar CRUD requests" in output_str
        assert "Latency of calendar provider calls" in output_str
        
        # Verify labels are present
        assert "provider=" in output_str or 'provider="' in output_str
        assert "endpoint=" in output_str or 'endpoint="' in output_str
        assert "status=" in output_str or 'status="' in output_str
        
        print("✓ Metrics endpoint content test passed")
        print("  - Found calendar_api_requests metric")
        print("  - Found calendar_api_response_time metric")
        print("  - Found expected labels and help text")
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_metrics_performance():
    """Test that metrics generation stays under 60s for CI."""
    try:
        from backend.metrics import calendar_api_requests_total, get_metrics_output
        
        start_time = time.time()
        
        # Simulate some load
        for i in range(100):
            calendar_api_requests_total.labels(
                provider=f"provider_{i % 3}",
                endpoint=f"endpoint_{i % 5}",
                status="success" if i % 2 == 0 else "error"
            ).inc()
            
            # Generate metrics every 10 iterations
            if i % 10 == 0:
                output = get_metrics_output()
                assert len(output) > 0
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Ensure CI stays under 60s (we'll use a much lower threshold for safety)
        assert duration < 5.0, f"Metrics generation too slow: {duration:.2f}s"
        
        print(f"✓ Metrics performance test passed ({duration:.3f}s)")
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False


def test_metrics_labels_and_buckets():
    """Test that metrics have correct labels and histogram buckets."""
    try:
        from backend.metrics import (
            calendar_api_requests_total,
            calendar_api_response_time,
        )
        
        # Test counter labels
        expected_counter_labels = {"provider", "endpoint", "status"}
        actual_counter_labels = set(calendar_api_requests_total._labelnames)
        assert expected_counter_labels == actual_counter_labels, \
            f"Counter labels mismatch: expected {expected_counter_labels}, got {actual_counter_labels}"
        
        # Test histogram labels
        expected_histogram_labels = {"provider"}
        actual_histogram_labels = set(calendar_api_response_time._labelnames)
        assert expected_histogram_labels == actual_histogram_labels, \
            f"Histogram labels mismatch: expected {expected_histogram_labels}, got {actual_histogram_labels}"
        
        # Test histogram buckets
        expected_buckets = [0.1, 0.3, 0.5, 1.0, 2.0, 5.0, float('inf')]
        actual_buckets = list(calendar_api_response_time._upper_bounds)
        assert actual_buckets == expected_buckets, \
            f"Histogram buckets mismatch: expected {expected_buckets}, got {actual_buckets}"
        
        print("✓ Metrics labels and buckets test passed")
        print(f"  - Counter labels: {actual_counter_labels}")
        print(f"  - Histogram labels: {actual_histogram_labels}")
        print(f"  - Histogram buckets: {actual_buckets}")
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False


def main():
    """Run all CI tests."""
    print("Running CI metrics tests...\n")
    
    tests = [
        test_metrics_endpoint_content,
        test_metrics_performance,
        test_metrics_labels_and_buckets
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All CI tests passed!")
        print("✓ Metrics endpoint returns expected metric names")
        print("✓ CI performance stays under 60s")
        return 0
    else:
        print("❌ Some CI tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
