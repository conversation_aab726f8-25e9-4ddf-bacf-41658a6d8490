from unittest import mock

from pi_lawyer.utils.storage_utils import StorageClient


def test_generate_gcs_path():
    client = StorageClient()
    path = client.generate_gcs_path("tenant", "case", "cat", "file")
    assert "tenant" in path


@mock.patch("pi_lawyer.utils.storage_utils.StorageClient.upload_file")
def test_upload_file_mocked(mock_upload):
    client = StorageClient()
    mock_upload.return_value = "gs://bucket/file"
    result = client.upload_file(b"data", "file")
    assert result == "gs://bucket/file"


# TODO: Add more coverage for signed_url, delete_file, list_files.


def test_storage_utils_module_imports():
    from pi_lawyer.utils import storage_utils

    # Module should export the StorageClient class
    assert hasattr(storage_utils, "StorageClient")


# Add more targeted tests when function signatures are known
