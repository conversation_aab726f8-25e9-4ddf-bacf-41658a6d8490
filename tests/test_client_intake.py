"""
Tests for the client intake functionality.
This test suite covers the `create_client_intake` Supabase function.
"""
import os
from datetime import datetime

import pytest
from dotenv import load_dotenv

from supabase import Client, create_client

# Load environment variables
load_dotenv()

# Supabase setup
SUPABASE_URL = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
SUPABASE_KEY = os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY")


@pytest.fixture
def supabase_client() -> Client:
    """Create a Supabase client for testing."""
    if not SUPABASE_URL or not SUPABASE_KEY:
        pytest.skip("Supabase credentials not available")
    client = create_client(SUPABASE_URL, SUPABASE_KEY)
    if hasattr(client, "schema"):
        client.schema("tenants")
    return client


def generate_test_client(overrides=None):
    """Generate test client data."""
    data = {
        "first_name": f"Test{datetime.now().timestamp()}",
        "last_name": f"Client{datetime.now().timestamp()}",
        "date_of_birth": "1980-01-01",
        "email": f"test.client.{datetime.now().timestamp()}@example.com",
        "phone_primary": f"512-555-{int(1000 + datetime.now().timestamp() % 9000)}",
        "address": {
            "street": "123 Test St",
            "city": "Test City",
            "state": "TX",
            "zip": "78701",
        },
        "occupation": "Software Tester",
        "employer": "Test Inc.",
        "work_status": "full_time",
        "status": "active",
        "client_type": "individual",  # Added required field
        "intake_date": datetime.now().strftime("%Y-%m-%d"),
        "conflict_check_status": "pending",
        "tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # Added for testing
    }

    if overrides:
        data.update(overrides)

    return data


def generate_test_case(overrides=None):
    """Generate test case data."""
    timestamp = datetime.now().timestamp()
    data = {
        "title": f"Test Case {timestamp}",
        "description": "This is a test case for automated testing",
        "practice_area": "personal_injury",
        "case_type": "auto_accident",
        "intake_priority": "medium",
        "status": "active",
        "previously_consulted": False,
        "metadata": {"internal_notes": "Test case created for automated testing"},
        "tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # Added for testing
    }

    if overrides:
        data.update(overrides)

    return data


def generate_test_party(overrides=None):
    """Generate test party data."""
    data = {
        "first_name": f"Test{datetime.now().timestamp()}",
        "last_name": f"Party{datetime.now().timestamp()}",
        "type": "other",
        "role": "defendant",
        "address": {},
        "tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # Added for testing
    }

    if overrides:
        data.update(overrides)

    return data


@pytest.mark.parametrize("test_id", ["basic_client_intake"])
def test_create_client_intake_basic(supabase_client, test_id):
    """Test basic client intake creation."""
    client_data = generate_test_client()
    case_data = generate_test_case()
    other_parties = []

    # Make sure client_data has an address field
    if "address" not in client_data or not client_data["address"]:
        client_data["address"] = {
            "street": "123 Test St",
            "city": "Test City",
            "state": "TX",
            "zip": "78701",
        }

    # Print parameters for debugging
    print("Calling function with parameters:")
    print(f"Client data: {client_data}")
    print(f"Case data: {case_data}")

    response = supabase_client.rpc(
        "create_client_intake",
        {
            "p_client_data": client_data,
            "p_case_data": case_data,
            "p_other_parties": other_parties,
            "p_test_tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # Added for testing
            "p_test_user_id": "550e8400-e29b-41d4-a716-446655440000",  # Added for created_by field
        },
    ).execute()

    # Print response for debugging
    print(f"Response: {response}")
    if hasattr(response, "error"):
        print(f"Error: {response.error}")
    if hasattr(response, "data"):
        print(f"Data: {response.data}")

    # Handle response
    assert (
        not hasattr(response, "error") or not response.error
    ), f"Error creating client intake: {response.error}"
    data = response.data
    assert data.get("success"), "Response does not indicate success"
    assert "client_id" in data, "Client ID not returned"
    assert "case_id" in data, "Case ID not returned"

    print(f"Created client ID: {data['client_id']}")
    print(f"Created case ID: {data['case_id']}")

    # Cleanup would be ideal but requires admin access
    # This test will create data that needs to be cleaned up manually


@pytest.mark.parametrize("test_id", ["client_intake_with_other_parties"])
def test_create_client_intake_with_parties(supabase_client, test_id):
    """Test client intake creation with other parties."""
    client_data = generate_test_client()
    case_data = generate_test_case()
    other_parties = [
        generate_test_party({"role": "defendant"}),
        generate_test_party({"role": "witness"}),
    ]

    # Make sure client_data has an address field
    if "address" not in client_data or not client_data["address"]:
        client_data["address"] = {
            "street": "123 Test St",
            "city": "Test City",
            "state": "TX",
            "zip": "78701",
        }

    # Print parameters for debugging
    print("Calling function with parameters:")
    print(f"Client data: {client_data}")
    print(f"Case data: {case_data}")
    print(f"Other parties: {other_parties}")

    response = supabase_client.rpc(
        "create_client_intake",
        {
            "p_client_data": client_data,
            "p_case_data": case_data,
            "p_other_parties": other_parties,
            "p_test_tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # Added for testing
            "p_test_user_id": "550e8400-e29b-41d4-a716-446655440000",  # Added for created_by field
        },
    ).execute()

    # Print response for debugging
    print(f"Response: {response}")
    if hasattr(response, "error"):
        print(f"Error: {response.error}")
    if hasattr(response, "data"):
        print(f"Data: {response.data}")

    # Handle response
    assert (
        not hasattr(response, "error") or not response.error
    ), f"Error creating client intake: {response.error}"
    data = response.data
    assert data.get("success"), "Response does not indicate success"
    assert "client_id" in data, "Client ID not returned"
    assert "case_id" in data, "Case ID not returned"

    print(f"Created client ID: {data['client_id']}")
    print(f"Created case ID: {data['case_id']}")


@pytest.mark.parametrize("test_id", ["client_intake_missing_required_fields"])
def test_create_client_intake_missing_fields(supabase_client, test_id):
    """Test client intake with missing required fields."""
    client_data = generate_test_client()
    case_data = generate_test_case()

    # Remove required fields
    del client_data["first_name"]
    del client_data["last_name"]

    # Make sure client_data has an address field
    if "address" not in client_data or not client_data["address"]:
        client_data["address"] = {
            "street": "123 Test St",
            "city": "Test City",
            "state": "TX",
            "zip": "78701",
        }

    # Print parameters for debugging
    print("Calling function with parameters (missing required fields):")
    print(f"Client data: {client_data}")
    print(f"Case data: {case_data}")

    response = supabase_client.rpc(
        "create_client_intake",
        {
            "p_client_data": client_data,
            "p_case_data": case_data,
            "p_other_parties": [],
            "p_test_tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # Added for testing
            "p_test_user_id": "550e8400-e29b-41d4-a716-446655440000",  # Added for created_by field
        },
    ).execute()

    # Print response for debugging
    print(f"Response (missing fields): {response}")
    if hasattr(response, "error"):
        print(f"Error: {response.error}")
    if hasattr(response, "data"):
        print(f"Data: {response.data}")

    # For this test, we expect an error or a data response with success=false
    if hasattr(response, "error") and response.error:
        print(f"Got expected error: {response.error}")
        assert True  # Test passes if we got an error
    elif hasattr(response, "data") and response.data:
        # Alternatively, we might get a data response with success=false
        assert not response.data.get(
            "success"
        ), "Expected failure but got success response"
        print(f"Got expected failure response: {response.data}")
    else:
        assert False, "Expected error or failure response but got neither"
